<?php
// +----------------------------------------------------------------------
// | 商家端菜单设置
// +----------------------------------------------------------------------
return [
    [
        'name' => 'POINTS_MANAGER',
        'title' => '积分管理',
        'url' => 'pointsmanager://shop/points/index',
        'parent' => 'MEMBER_CENTER',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => '',
        'picture_select' => '',
        'sort' => 100,
        'child_list' => [
            [
                'name' => 'POINTS_RECORDS',
                'title' => '积分记录',
                'url' => 'pointsmanager://shop/points/records',
                'sort' => 1,
                'is_show' => 1,
                'type' => 'menu',
            ],
            [
                'name' => 'POINTS_RULES',
                'title' => '积分规则',
                'url' => 'pointsmanager://shop/points/rules',
                'sort' => 2,
                'is_show' => 1,
                'type' => 'menu',
            ],
            [
                'name' => 'POINTS_STATISTICS',
                'title' => '积分统计',
                'url' => 'pointsmanager://shop/points/statistics',
                'sort' => 3,
                'is_show' => 1,
                'type' => 'menu',
            ],
            [
                'name' => 'POINTS_ADD',
                'title' => '添加积分',
                'url' => 'pointsmanager://shop/points/add',
                'sort' => 4,
                'is_show' => 0,
                'type' => 'button',
            ],
            [
                'name' => 'POINTS_EDIT',
                'title' => '编辑积分',
                'url' => 'pointsmanager://shop/points/edit',
                'sort' => 5,
                'is_show' => 0,
                'type' => 'button',
            ],
            [
                'name' => 'POINTS_DELETE',
                'title' => '删除积分',
                'url' => 'pointsmanager://shop/points/delete',
                'sort' => 6,
                'is_show' => 0,
                'type' => 'button',
            ]
        ]
    ]
];
