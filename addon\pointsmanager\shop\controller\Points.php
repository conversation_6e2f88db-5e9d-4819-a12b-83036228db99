<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\pointsmanager\shop\controller;

use app\shop\controller\BaseShop;
use addon\pointsmanager\model\PointsRecord;
use addon\pointsmanager\model\PointsRule;

/**
 * 积分管理控制器
 */
class Points extends BaseShop
{
    /**
     * 积分管理首页
     */
    public function index()
    {
        if (request()->isAjax()) {
            $points_record = new PointsRecord();
            $condition = [
                ['site_id', '=', $this->site_id]
            ];
            
            // 获取统计数据
            $statistics = $points_record->getStatistics($condition);
            
            return $statistics;
        }
        
        return $this->fetch('points/index');
    }

    /**
     * 积分记录列表
     */
    public function records()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $member_id = input('member_id', 0);
            $type = input('type', '');
            $source = input('source', '');
            
            $condition = [
                ['site_id', '=', $this->site_id]
            ];
            
            if ($member_id > 0) {
                $condition[] = ['member_id', '=', $member_id];
            }
            
            if ($type !== '') {
                $condition[] = ['type', '=', $type];
            }
            
            if (!empty($source)) {
                $condition[] = ['source', 'like', '%' . $source . '%'];
            }
            
            $points_record = new PointsRecord();
            $list = $points_record->getRecordList($condition, '*', 'create_time desc');
            
            if ($list['code'] == 0) {
                // 分页处理
                $total = count($list['data']);
                $start = ($page - 1) * $page_size;
                $list['data'] = array_slice($list['data'], $start, $page_size);
                
                return success([
                    'list' => $list['data'],
                    'total' => $total,
                    'page' => $page,
                    'page_size' => $page_size
                ]);
            }
            
            return $list;
        }
        
        return $this->fetch('points/records');
    }

    /**
     * 积分规则管理
     */
    public function rules()
    {
        if (request()->isAjax()) {
            $points_rule = new PointsRule();
            $condition = [
                ['site_id', '=', $this->site_id]
            ];
            
            $list = $points_rule->getRuleList($condition);
            return $list;
        }
        
        return $this->fetch('points/rules');
    }

    /**
     * 积分统计
     */
    public function statistics()
    {
        if (request()->isAjax()) {
            $points_record = new PointsRecord();
            $condition = [
                ['site_id', '=', $this->site_id]
            ];
            
            $statistics = $points_record->getStatistics($condition);
            return $statistics;
        }
        
        return $this->fetch('points/statistics');
    }

    /**
     * 添加积分规则
     */
    public function addRule()
    {
        if (request()->isPost()) {
            $data = [
                'site_id' => $this->site_id,
                'name' => input('name', ''),
                'code' => input('code', ''),
                'points' => input('points', 0),
                'status' => input('status', 1),
                'remark' => input('remark', ''),
            ];
            
            // 验证数据
            if (empty($data['name'])) {
                return error('', '规则名称不能为空');
            }
            
            if (empty($data['code'])) {
                return error('', '规则代码不能为空');
            }
            
            if ($data['points'] <= 0) {
                return error('', '积分数量必须大于0');
            }
            
            $points_rule = new PointsRule();
            return $points_rule->addRule($data);
        }
        
        return $this->fetch('points/add_rule');
    }

    /**
     * 编辑积分规则
     */
    public function editRule()
    {
        $id = input('id', 0);
        
        if (request()->isPost()) {
            $data = [
                'name' => input('name', ''),
                'code' => input('code', ''),
                'points' => input('points', 0),
                'status' => input('status', 1),
                'remark' => input('remark', ''),
            ];
            
            // 验证数据
            if (empty($data['name'])) {
                return error('', '规则名称不能为空');
            }
            
            if (empty($data['code'])) {
                return error('', '规则代码不能为空');
            }
            
            if ($data['points'] <= 0) {
                return error('', '积分数量必须大于0');
            }
            
            $points_rule = new PointsRule();
            return $points_rule->editRule($id, $data);
        }
        
        $points_rule = new PointsRule();
        $info = $points_rule->getRuleInfo($id);
        
        $this->assign('info', $info['data'] ?? []);
        return $this->fetch('points/edit_rule');
    }

    /**
     * 删除积分规则
     */
    public function deleteRule()
    {
        $id = input('id', 0);
        
        if ($id <= 0) {
            return error('', '参数错误');
        }
        
        $points_rule = new PointsRule();
        return $points_rule->deleteRule($id);
    }

    /**
     * 手动添加积分
     */
    public function addPoints()
    {
        if (request()->isPost()) {
            $member_id = input('member_id', 0);
            $points = input('points', 0);
            $remark = input('remark', '');
            
            if ($member_id <= 0) {
                return error('', '请选择会员');
            }
            
            if ($points == 0) {
                return error('', '积分数量不能为0');
            }
            
            $points_record = new PointsRecord();
            return $points_record->addPointsToMember($member_id, $points, '手动添加', $remark);
        }
        
        return $this->fetch('points/add_points');
    }
}
