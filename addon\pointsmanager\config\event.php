<?php
// 事件定义文件
return [
    'bind' => [
    
    ],
    
    'listen' => [
        // 会员注册事件 - 赠送注册积分
        'MemberRegister' => [
            'addon\pointsmanager\event\MemberRegister',
        ],
        // 订单支付事件 - 赠送消费积分
        'OrderPay' => [
            'addon\pointsmanager\event\OrderPay',
        ],
        // 会员签到事件 - 赠送签到积分
        'MemberSignin' => [
            'addon\pointsmanager\event\MemberSignin',
        ],
        // 积分变动通知事件
        'PointsChange' => [
            'addon\pointsmanager\event\PointsChange',
        ],
    ],
    
    'subscribe' => [
    ],
];
