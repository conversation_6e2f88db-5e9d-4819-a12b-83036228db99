<?php
//000000000000
 exit();?>
a:60:{i:0;a:5:{s:7:"OssType";a:1:{i:0;s:26:"addon\alioss\event\OssType";}s:3:"Put";a:1:{i:0;s:22:"addon\alioss\event\Put";}s:8:"CloseOss";a:1:{i:0;s:27:"addon\alioss\event\CloseOss";}s:13:"ClearAlbumPic";a:1:{i:0;s:32:"addon\alioss\event\ClearAlbumPic";}s:13:"ShowPromotion";a:1:{i:0;s:32:"addon\alioss\event\ShowPromotion";}}i:1;a:9:{s:9:"PayNotify";a:1:{i:0;s:28:"addon\alipay\event\PayNotify";}s:7:"PayType";a:1:{i:0;s:26:"addon\alipay\event\PayType";}s:3:"Pay";a:1:{i:0;s:22:"addon\alipay\event\Pay";}s:8:"PayClose";a:1:{i:0;s:27:"addon\alipay\event\PayClose";}s:9:"PayRefund";a:1:{i:0;s:28:"addon\alipay\event\PayRefund";}s:11:"PayTransfer";a:1:{i:0;s:30:"addon\alipay\event\PayTransfer";}s:12:"TransferType";a:1:{i:0;s:31:"addon\alipay\event\TransferType";}s:11:"AuthcodePay";a:1:{i:0;s:30:"addon\alipay\event\AuthcodePay";}s:13:"PayOrderQuery";a:1:{i:0;s:32:"addon\alipay\event\PayOrderQuery";}}i:2;a:6:{s:7:"SmsType";a:1:{i:0;s:26:"addon\alisms\event\SmsType";}s:16:"DoEditSmsMessage";a:1:{i:0;s:35:"addon\alisms\event\DoEditSmsMessage";}s:7:"SendSms";a:1:{i:0;s:26:"addon\alisms\event\SendSms";}s:14:"EnableCallBack";a:1:{i:0;s:33:"addon\alisms\event\EnableCallBack";}s:9:"EnableSms";a:1:{i:0;s:28:"addon\alisms\event\EnableSms";}s:15:"SmsTemplateInfo";a:1:{i:0;s:34:"addon\alisms\event\SmsTemplateInfo";}}i:3;a:4:{s:13:"ShowPromotion";a:1:{i:0;s:30:"addon\bale\event\ShowPromotion";}s:8:"OpenBale";a:1:{i:0;s:25:"addon\bale\event\OpenBale";}s:9:"CloseBale";a:1:{i:0;s:26:"addon\bale\event\CloseBale";}s:18:"OrderPromotionType";a:1:{i:0;s:35:"addon\bale\event\OrderPromotionType";}}i:4;a:13:{s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\bargain\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:33:"addon\bargain\event\PromotionType";}s:12:"CloseBargain";a:1:{i:0;s:32:"addon\bargain\event\CloseBargain";}s:11:"OpenBargain";a:1:{i:0;s:31:"addon\bargain\event\OpenBargain";}s:18:"BargainLaunchClose";a:1:{i:0;s:38:"addon\bargain\event\BargainLaunchClose";}s:18:"GoodsPromotionType";a:1:{i:0;s:38:"addon\bargain\event\GoodsPromotionType";}s:14:"GoodsPromotion";a:1:{i:0;s:34:"addon\bargain\event\GoodsPromotion";}s:18:"GoodsListPromotion";a:1:{i:0;s:38:"addon\bargain\event\GoodsListPromotion";}s:20:"GoodsListCategoryIds";a:1:{i:0;s:40:"addon\bargain\event\GoodsListCategoryIds";}s:18:"OrderPromotionType";a:1:{i:0;s:38:"addon\bargain\event\OrderPromotionType";}s:13:"OrderPayAfter";a:1:{i:0;s:33:"addon\bargain\event\OrderPayAfter";}s:19:"SendMessageTemplate";a:1:{i:0;s:42:"addon\bargain\event\MessageBargainComplete";}s:19:"PromotionZoneConfig";a:1:{i:0;s:37:"addon\bargain\event\BargainZoneConfig";}}i:5;a:4:{s:13:"ShowPromotion";a:1:{i:0;s:38:"addon\birthdaygift\event\ShowPromotion";}s:9:"PointRule";a:1:{i:0;s:34:"addon\birthdaygift\event\PointRule";}s:16:"OpenBirthdayGift";a:1:{i:0;s:41:"addon\birthdaygift\event\OpenBirthdayGift";}s:17:"CloseBirthdayGift";a:1:{i:0;s:42:"addon\birthdaygift\event\CloseBirthdayGift";}}i:6;a:4:{s:13:"ShowPromotion";a:1:{i:0;s:34:"addon\bundling\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:34:"addon\bundling\event\PromotionType";}s:18:"OrderPromotionType";a:1:{i:0;s:39:"addon\bundling\event\OrderPromotionType";}s:11:"DeleteGoods";a:1:{i:0;s:32:"addon\bundling\event\DeleteGoods";}}i:7;a:3:{s:13:"ShowPromotion";a:1:{i:0;s:31:"addon\cards\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:31:"addon\cards\event\PromotionType";}s:21:"MemberAccountFromType";a:1:{i:0;s:39:"addon\cards\event\MemberAccountFromType";}}i:8;a:11:{s:13:"ShowPromotion";a:1:{i:0;s:37:"addon\cardservice\event\ShowPromotion";}s:10:"GoodsClass";a:2:{i:0;s:41:"addon\cardservice\event\ServiceGoodsClass";i:1;s:38:"addon\cardservice\event\CardGoodsClass";}s:6:"Verify";a:1:{i:0;s:39:"addon\cardservice\event\CardGoodsVerify";}s:10:"VerifyType";a:1:{i:0;s:34:"addon\cardservice\event\VerifyType";}s:20:"CronMemberCardExpire";a:1:{i:0;s:44:"addon\cardservice\event\CronMemberCardExpire";}s:18:"OrderPromotionType";a:1:{i:0;s:42:"addon\cardservice\event\OrderPromotionType";}s:19:"PromotionZoneConfig";a:1:{i:0;s:45:"addon\cardservice\event\CardServiceZoneConfig";}s:12:"MemberDetail";a:1:{i:0;s:36:"addon\cardservice\event\MemberDetail";}s:17:"OrderRefundFinish";a:1:{i:0;s:41:"addon\cardservice\event\OrderRefundFinish";}s:10:"OrderClose";a:1:{i:0;s:34:"addon\cardservice\event\OrderClose";}s:16:"DeleteGoodsCheck";a:1:{i:0;s:40:"addon\cardservice\event\DeleteGoodsCheck";}}i:9;a:14:{s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\cashier\event\ShowPromotion";}s:21:"CashierOrderPayNotify";a:1:{i:0;s:41:"addon\cashier\event\CashierOrderPayNotify";}s:12:"TradePayType";a:1:{i:0;s:32:"addon\cashier\event\TradePayType";}s:9:"PayRefund";a:1:{i:0;s:29:"addon\cashier\event\PayRefund";}s:19:"PrinterTemplateType";a:1:{i:0;s:39:"addon\cashier\event\PrinterTemplateType";}s:11:"PrinterHtml";a:1:{i:0;s:31:"addon\cashier\event\PrinterHtml";}s:14:"PrinterContent";a:1:{i:0;s:34:"addon\cashier\event\PrinterContent";}s:13:"GetOrderModel";a:1:{i:0;s:33:"addon\cashier\event\GetOrderModel";}s:16:"OrderGoodsRefund";a:1:{i:0;s:36:"addon\cashier\event\OrderGoodsRefund";}s:13:"OrderFromList";a:1:{i:0;s:33:"addon\cashier\event\OrderFromList";}s:16:"IncomeStatistics";a:1:{i:0;s:36:"addon\cashier\event\IncomeStatistics";}s:15:"CronOrderDelete";a:1:{i:0;s:35:"addon\cashier\event\CronOrderDelete";}s:12:"GetOrderType";a:1:{i:0;s:32:"addon\cashier\event\GetOrderType";}s:8:"OrderPay";a:1:{i:0;s:28:"addon\cashier\event\OrderPay";}}i:10;a:5:{s:13:"ShowPromotion";a:1:{i:0;s:32:"addon\coupon\event\ShowPromotion";}s:13:"CronCouponEnd";a:1:{i:0;s:32:"addon\coupon\event\CronCouponEnd";}s:17:"CronCouponTypeEnd";a:1:{i:0;s:36:"addon\coupon\event\CronCouponTypeEnd";}s:7:"AddStat";a:1:{i:0;s:26:"addon\coupon\event\AddStat";}s:13:"PromotionPage";a:1:{i:0;s:32:"addon\coupon\event\PromotionPage";}}i:11;a:5:{s:12:"OpenDiscount";a:1:{i:0;s:33:"addon\discount\event\OpenDiscount";}s:13:"CloseDiscount";a:1:{i:0;s:34:"addon\discount\event\CloseDiscount";}s:13:"ShowPromotion";a:1:{i:0;s:34:"addon\discount\event\ShowPromotion";}s:18:"GoodsPromotionType";a:1:{i:0;s:39:"addon\discount\event\GoodsPromotionType";}s:14:"GoodsPromotion";a:1:{i:0;s:35:"addon\discount\event\GoodsPromotion";}}i:12;a:5:{s:13:"ShowPromotion";a:1:{i:0;s:38:"addon\divideticket\event\ShowPromotion";}s:17:"CloseDivideTicket";a:1:{i:0;s:42:"addon\divideticket\event\CloseDivideticket";}s:28:"CronChangeDivideticketStatus";a:1:{i:0;s:53:"addon\divideticket\event\CronChangeDivideticketStatus";}s:23:"DivideticketLaunchClose";a:1:{i:0;s:48:"addon\divideticket\event\DivideticketLaunchClose";}s:22:"DivideticketSimulation";a:1:{i:0;s:47:"addon\divideticket\event\DivideticketSimulation";}}i:13;a:3:{s:13:"ShowPromotion";a:1:{i:0;s:29:"addon\egg\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:29:"addon\egg\event\PromotionType";}s:21:"MemberAccountFromType";a:1:{i:0;s:37:"addon\egg\event\MemberAccountFromType";}}i:14;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:41:"addon\electronicsheet\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:41:"addon\electronicsheet\event\PromotionType";}}i:15;a:21:{s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\fenxiao\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:33:"addon\fenxiao\event\PromotionType";}s:13:"OrderComplete";a:2:{i:0;s:35:"addon\fenxiao\event\OrderSettlement";i:1;s:33:"addon\fenxiao\event\OrderComplete";}s:17:"OrderRefundFinish";a:1:{i:0;s:36:"addon\fenxiao\event\OrderGoodsRefund";}s:18:"AlterShareRelation";a:1:{i:0;s:38:"addon\fenxiao\event\AlterShareRelation";}s:16:"OrderCreateAfter";a:1:{i:0;s:36:"addon\fenxiao\event\OrderCreateAfter";}s:18:"PresaleOrderCreate";a:1:{i:0;s:38:"addon\fenxiao\event\PresaleOrderCreate";}s:13:"OrderPayAfter";a:1:{i:0;s:33:"addon\fenxiao\event\OrderPayAfter";}s:21:"MemberAccountFromType";a:1:{i:0;s:41:"addon\fenxiao\event\MemberAccountFromType";}s:14:"MemberRegister";a:1:{i:0;s:34:"addon\fenxiao\event\MemberRegister";}s:14:"FenxiaoUpgrade";a:1:{i:0;s:34:"addon\fenxiao\event\FenxiaoUpgrade";}s:7:"AddSite";a:2:{i:0;s:34:"addon\fenxiao\event\AddSiteDiyView";i:1;s:39:"addon\fenxiao\event\AddSiteFenxiaoLevel";}s:18:"GoodsListPromotion";a:1:{i:0;s:38:"addon\fenxiao\event\GoodsListPromotion";}s:20:"GoodsListCategoryIds";a:1:{i:0;s:40:"addon\fenxiao\event\GoodsListCategoryIds";}s:12:"MemberCancel";a:1:{i:0;s:32:"addon\fenxiao\event\MemberCancel";}s:14:"WchatShareData";a:1:{i:0;s:34:"addon\fenxiao\event\WchatShareData";}s:16:"WchatShareConfig";a:1:{i:0;s:36:"addon\fenxiao\event\WchatShareConfig";}s:14:"WeappShareData";a:1:{i:0;s:34:"addon\fenxiao\event\WeappShareData";}s:16:"WeappShareConfig";a:1:{i:0;s:36:"addon\fenxiao\event\WeappShareConfig";}s:7:"AddStat";a:1:{i:0;s:27:"addon\fenxiao\event\AddStat";}s:17:"PayTransferNotify";a:1:{i:0;s:42:"addon\fenxiao\event\WithdrawTransferNotify";}}i:16;a:3:{s:13:"ShowPromotion";a:1:{i:0;s:30:"addon\form\event\ShowPromotion";}s:12:"OrderPayment";a:1:{i:0;s:29:"addon\form\event\OrderPayment";}s:16:"OrderCreateAfter";a:1:{i:0;s:33:"addon\form\event\OrderCreateAfter";}}i:17;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:38:"addon\freeshipping\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:38:"addon\freeshipping\event\PromotionType";}}i:18;a:10:{s:13:"ShowPromotion";a:1:{i:0;s:34:"addon\giftcard\event\ShowPromotion";}s:18:"GiftCardOrderClose";a:1:{i:0;s:39:"addon\giftcard\event\GiftCardOrderClose";}s:22:"GiftCardOrderPayNotify";a:1:{i:0;s:43:"addon\giftcard\event\GiftCardOrderPayNotify";}s:16:"IncomeStatistics";a:1:{i:0;s:37:"addon\giftcard\event\IncomeStatistics";}s:7:"AddStat";a:1:{i:0;s:28:"addon\giftcard\event\AddStat";}s:14:"CronCardExpire";a:1:{i:0;s:35:"addon\giftcard\event\CronCardExpire";}s:8:"PayReset";a:1:{i:0;s:29:"addon\giftcard\event\PayReset";}s:13:"OrderPayAfter";a:1:{i:0;s:34:"addon\giftcard\event\OrderPayAfter";}s:18:"OrderPromotionType";a:1:{i:0;s:39:"addon\giftcard\event\OrderPromotionType";}s:27:"WapOrderDetailPathByPayInfo";a:1:{i:0;s:48:"addon\giftcard\event\WapOrderDetailPathByPayInfo";}}i:19;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:37:"addon\goodscircle\event\ShowPromotion";}s:9:"WeappMenu";a:1:{i:0;s:33:"addon\goodscircle\event\WeappMenu";}}i:20;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:35:"addon\goodsgrab\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:35:"addon\goodsgrab\event\PromotionType";}}i:21;a:11:{s:13:"ShowPromotion";a:1:{i:0;s:34:"addon\groupbuy\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:34:"addon\groupbuy\event\PromotionType";}s:13:"CloseGroupbuy";a:1:{i:0;s:34:"addon\groupbuy\event\CloseGroupbuy";}s:12:"OpenGroupbuy";a:1:{i:0;s:33:"addon\groupbuy\event\OpenGroupbuy";}s:18:"GoodsPromotionType";a:1:{i:0;s:39:"addon\groupbuy\event\GoodsPromotionType";}s:14:"GoodsPromotion";a:1:{i:0;s:35:"addon\groupbuy\event\GoodsPromotion";}s:18:"GoodsListPromotion";a:1:{i:0;s:39:"addon\groupbuy\event\GoodsListPromotion";}s:20:"GoodsListCategoryIds";a:1:{i:0;s:41:"addon\groupbuy\event\GoodsListCategoryIds";}s:18:"OrderPromotionType";a:1:{i:0;s:39:"addon\groupbuy\event\OrderPromotionType";}s:13:"OrderPayAfter";a:1:{i:0;s:34:"addon\groupbuy\event\OrderPayAfter";}s:19:"PromotionZoneConfig";a:1:{i:0;s:39:"addon\groupbuy\event\GroupBuyZoneConfig";}}i:22;a:7:{s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\jielong\event\ShowPromotion";}s:11:"OpenJielong";a:1:{i:0;s:31:"addon\jielong\event\OpenJielong";}s:12:"CloseJielong";a:1:{i:0;s:32:"addon\jielong\event\CloseJielong";}s:18:"GoodsPromotionType";a:1:{i:0;s:38:"addon\jielong\event\GoodsPromotionType";}s:16:"OrderCreateAfter";a:1:{i:0;s:36:"addon\jielong\event\OrderCreateAfter";}s:8:"OrderPay";a:1:{i:0;s:28:"addon\jielong\event\OrderPay";}s:10:"OrderClose";a:1:{i:0;s:30:"addon\jielong\event\OrderClose";}}i:23;a:4:{s:13:"ShowPromotion";a:1:{i:0;s:30:"addon\live\event\ShowPromotion";}s:9:"WeappMenu";a:1:{i:0;s:26:"addon\live\event\WeappMenu";}s:15:"LiveGoodsStatus";a:1:{i:0;s:32:"addon\live\event\LiveGoodsStatus";}s:14:"LiveRoomStatus";a:1:{i:0;s:31:"addon\live\event\LiveRoomStatus";}}i:24;a:6:{s:11:"OpenManjian";a:1:{i:0;s:31:"addon\manjian\event\OpenManjian";}s:12:"CloseManjian";a:1:{i:0;s:32:"addon\manjian\event\CloseManjian";}s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\manjian\event\ShowPromotion";}s:13:"OrderPayAfter";a:1:{i:0;s:33:"addon\manjian\event\OrderPayAfter";}s:21:"MemberAccountFromType";a:1:{i:0;s:41:"addon\manjian\event\MemberAccountFromType";}s:20:"OrderRefundAllFinish";a:1:{i:0;s:40:"addon\manjian\event\OrderRefundAllFinish";}}i:25;a:1:{s:13:"ShowPromotion";a:1:{i:0;s:38:"addon\membercancel\event\ShowPromotion";}}i:26;a:9:{s:13:"OrderPayAfter";a:1:{i:0;s:39:"addon\memberconsume\event\OrderPayAfter";}s:16:"GiftCardOrderPay";a:1:{i:0;s:39:"addon\memberconsume\event\OrderPayAfter";}s:21:"BlindboxGoodsOrderPay";a:1:{i:0;s:39:"addon\memberconsume\event\OrderPayAfter";}s:12:"MemberAction";a:1:{i:0;s:38:"addon\memberconsume\event\MemberAction";}s:13:"ShowPromotion";a:1:{i:0;s:39:"addon\memberconsume\event\ShowPromotion";}s:21:"MemberAccountFromType";a:1:{i:0;s:47:"addon\memberconsume\event\MemberAccountFromType";}s:17:"MemberAccountRule";a:1:{i:0;s:43:"addon\memberconsume\event\MemberAccountRule";}s:17:"OrderRefundFinish";a:1:{i:0;s:43:"addon\memberconsume\event\OrderRefundFinish";}s:9:"PointRule";a:1:{i:0;s:35:"addon\memberconsume\event\PointRule";}}i:27;a:1:{s:13:"PromotionType";a:1:{i:0;s:37:"addon\memberprice\event\PromotionType";}}i:28;a:10:{s:13:"ShowPromotion";a:1:{i:0;s:40:"addon\memberrecharge\event\ShowPromotion";}s:28:"MemberrechargeOrderPayNotify";a:1:{i:0;s:55:"addon\memberrecharge\event\MemberrechargeOrderPayNotify";}s:24:"MemberrechargeOrderClose";a:1:{i:0;s:51:"addon\memberrecharge\event\MemberrechargeOrderClose";}s:21:"MemberAccountFromType";a:1:{i:0;s:48:"addon\memberrecharge\event\MemberAccountFromType";}s:19:"PrinterTemplateType";a:1:{i:0;s:46:"addon\memberrecharge\event\PrinterTemplateType";}s:11:"PrinterHtml";a:1:{i:0;s:38:"addon\memberrecharge\event\PrinterHtml";}s:14:"PrinterContent";a:1:{i:0;s:41:"addon\memberrecharge\event\PrinterContent";}s:16:"IncomeStatistics";a:1:{i:0;s:43:"addon\memberrecharge\event\IncomeStatistics";}s:16:"CashierCalculate";a:1:{i:0;s:43:"addon\memberrecharge\event\CashierCalculate";}s:27:"WapOrderDetailPathByPayInfo";a:1:{i:0;s:54:"addon\memberrecharge\event\WapOrderDetailPathByPayInfo";}}i:29;a:5:{s:13:"OpenRecommend";a:1:{i:0;s:41:"addon\memberrecommend\event\OpenRecommend";}s:14:"CloseRecommend";a:1:{i:0;s:42:"addon\memberrecommend\event\CloseRecommend";}s:13:"ShowPromotion";a:1:{i:0;s:41:"addon\memberrecommend\event\ShowPromotion";}s:21:"MemberAccountFromType";a:1:{i:0;s:49:"addon\memberrecommend\event\MemberAccountFromType";}s:14:"MemberRegister";a:1:{i:0;s:42:"addon\memberrecommend\event\MemberRegister";}}i:30;a:6:{s:12:"MemberAction";a:1:{i:0;s:39:"addon\memberregister\event\MemberAction";}s:13:"ShowPromotion";a:1:{i:0;s:40:"addon\memberregister\event\ShowPromotion";}s:19:"MemberRegisterAward";a:1:{i:0;s:46:"addon\memberregister\event\MemberRegisterAward";}s:17:"MemberAccountRule";a:1:{i:0;s:44:"addon\memberregister\event\MemberAccountRule";}s:25:"MemberReceiveRegisterGift";a:1:{i:0;s:52:"addon\memberregister\event\MemberReceiveRegisterGift";}s:9:"PointRule";a:1:{i:0;s:36:"addon\memberregister\event\PointRule";}}i:31;a:7:{s:12:"MemberAction";a:1:{i:0;s:39:"addon\memberregister\event\MemberAction";}s:13:"ShowPromotion";a:1:{i:0;s:38:"addon\membersignin\event\ShowPromotion";}s:17:"MemberSigninAward";a:1:{i:0;s:42:"addon\membersignin\event\MemberSigninAward";}s:12:"MemberSignin";a:1:{i:0;s:37:"addon\membersignin\event\MemberSignin";}s:21:"MemberAccountFromType";a:1:{i:0;s:46:"addon\membersignin\event\MemberAccountFromType";}s:17:"MemberAccountRule";a:1:{i:0;s:42:"addon\membersignin\event\MemberAccountRule";}s:9:"PointRule";a:1:{i:0;s:34:"addon\membersignin\event\PointRule";}}i:32;a:1:{s:17:"PayTransferNotify";a:1:{i:0;s:49:"addon\memberwithdraw\event\WithdrawTransferNotify";}}i:33;a:1:{s:13:"ShowPromotion";a:1:{i:0;s:36:"addon\mobileshop\event\ShowPromotion";}}i:34;a:7:{s:7:"SmsType";a:1:{i:0;s:26:"addon\niusms\event\SmsType";}s:16:"DoEditSmsMessage";a:1:{i:0;s:35:"addon\niusms\event\DoEditSmsMessage";}s:7:"SendSms";a:1:{i:0;s:26:"addon\niusms\event\SendSms";}s:15:"SmsTemplateInfo";a:1:{i:0;s:34:"addon\niusms\event\SmsTemplateInfo";}s:14:"EnableCallBack";a:1:{i:0;s:33:"addon\niusms\event\EnableCallBack";}s:9:"EnableSms";a:1:{i:0;s:28:"addon\niusms\event\EnableSms";}s:15:"CloseSmsPayment";a:1:{i:0;s:34:"addon\niusms\event\CloseSmsPayment";}}i:35;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:31:"addon\notes\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:31:"addon\notes\event\PromotionType";}}i:36;a:5:{s:7:"PayType";a:1:{i:0;s:30:"addon\offlinepay\event\PayType";}s:3:"Pay";a:1:{i:0;s:26:"addon\offlinepay\event\Pay";}s:8:"PayClose";a:1:{i:0;s:31:"addon\offlinepay\event\PayClose";}s:9:"PayRefund";a:1:{i:0;s:32:"addon\offlinepay\event\PayRefund";}s:19:"SendMessageTemplate";a:2:{i:0;s:49:"addon\offlinepay\event\MessageOfflinepayWaitAudit";i:1;s:51:"addon\offlinepay\event\MessageOfflinepayAuditRefuse";}}i:37;a:1:{s:13:"ShowPromotion";a:1:{i:0;s:28:"addon\pc\event\ShowPromotion";}}i:38;a:14:{s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\pintuan\event\ShowPromotion";}s:8:"OrderPay";a:1:{i:0;s:28:"addon\pintuan\event\OrderPay";}s:10:"OrderClose";a:1:{i:0;s:30:"addon\pintuan\event\OrderClose";}s:13:"PromotionType";a:1:{i:0;s:33:"addon\pintuan\event\PromotionType";}s:11:"OpenPintuan";a:1:{i:0;s:31:"addon\pintuan\event\OpenPintuan";}s:12:"ClosePintuan";a:1:{i:0;s:32:"addon\pintuan\event\ClosePintuan";}s:17:"ClosePintuanGroup";a:1:{i:0;s:37:"addon\pintuan\event\ClosePintuanGroup";}s:18:"GoodsPromotionType";a:1:{i:0;s:38:"addon\pintuan\event\GoodsPromotionType";}s:14:"GoodsPromotion";a:1:{i:0;s:34:"addon\pintuan\event\GoodsPromotion";}s:18:"GoodsListPromotion";a:1:{i:0;s:38:"addon\pintuan\event\GoodsListPromotion";}s:20:"GoodsListCategoryIds";a:1:{i:0;s:40:"addon\pintuan\event\GoodsListCategoryIds";}s:18:"OrderPromotionType";a:1:{i:0;s:38:"addon\pintuan\event\OrderPromotionType";}s:19:"SendMessageTemplate";a:2:{i:0;s:42:"addon\pintuan\event\MessagePintuanComplete";i:1;s:38:"addon\pintuan\event\MessagePintuanFail";}s:19:"PromotionZoneConfig";a:1:{i:0;s:37:"addon\pintuan\event\PintuanZoneConfig";}}i:39;a:3:{s:13:"ShowPromotion";a:1:{i:0;s:35:"addon\pointcash\event\ShowPromotion";}s:21:"MemberAccountFromType";a:1:{i:0;s:43:"addon\pointcash\event\MemberAccountFromType";}s:9:"PointRule";a:1:{i:0;s:31:"addon\pointcash\event\PointRule";}}i:40;a:8:{s:13:"ShowPromotion";a:1:{i:0;s:39:"addon\pointexchange\event\ShowPromotion";}s:27:"PointexchangeOrderPayNotify";a:1:{i:0;s:53:"addon\pointexchange\event\PointexchangeOrderPayNotify";}s:21:"MemberAccountFromType";a:1:{i:0;s:47:"addon\pointexchange\event\MemberAccountFromType";}s:10:"OrderClose";a:1:{i:0;s:36:"addon\pointexchange\event\OrderClose";}s:18:"OrderPromotionType";a:1:{i:0;s:44:"addon\pointexchange\event\OrderPromotionType";}s:13:"CouponGetType";a:1:{i:0;s:39:"addon\pointexchange\event\CouponGetType";}s:27:"WapOrderDetailPathByPayInfo";a:1:{i:0;s:53:"addon\pointexchange\event\WapOrderDetailPathByPayInfo";}s:16:"DeleteGoodsCheck";a:1:{i:0;s:42:"addon\pointexchange\event\DeleteGoodsCheck";}}i:41;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:40:"addon\postertemplate\event\ShowPromotion";}s:14:"PosterTemplate";a:1:{i:0;s:41:"addon\postertemplate\event\PosterTemplate";}}i:42;a:15:{s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\presale\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:33:"addon\presale\event\PromotionType";}s:12:"ClosePresale";a:1:{i:0;s:32:"addon\presale\event\ClosePresale";}s:11:"OpenPresale";a:1:{i:0;s:31:"addon\presale\event\OpenPresale";}s:18:"GoodsPromotionType";a:1:{i:0;s:38:"addon\presale\event\GoodsPromotionType";}s:21:"CronDepositOrderClose";a:1:{i:0;s:37:"addon\presale\event\DepositOrderClose";}s:21:"DepositOrderPayNotify";a:1:{i:0;s:41:"addon\presale\event\DepositOrderPayNotify";}s:19:"FinalOrderPayNotify";a:1:{i:0;s:39:"addon\presale\event\FinalOrderPayNotify";}s:16:"AddonOrderRefund";a:1:{i:0;s:36:"addon\presale\event\AddonOrderRefund";}s:18:"GoodsListPromotion";a:1:{i:0;s:38:"addon\presale\event\GoodsListPromotion";}s:20:"GoodsListCategoryIds";a:1:{i:0;s:40:"addon\presale\event\GoodsListCategoryIds";}s:21:"MemberAccountFromType";a:1:{i:0;s:41:"addon\presale\event\MemberAccountFromType";}s:18:"OrderPromotionType";a:1:{i:0;s:38:"addon\presale\event\OrderPromotionType";}s:13:"IsJoinPresale";a:1:{i:0;s:33:"addon\presale\event\IsJoinPresale";}s:27:"CronRefundOrderDepositClose";a:1:{i:0;s:47:"addon\presale\event\CronRefundOrderDepositClose";}}i:43;a:11:{s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\printer\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:33:"addon\printer\event\PromotionType";}s:10:"PrintOrder";a:1:{i:0;s:30:"addon\printer\event\PrintOrder";}s:15:"PrinterTemplate";a:1:{i:0;s:35:"addon\printer\event\PrinterTemplate";}s:14:"PrinterContent";a:1:{i:0;s:34:"addon\printer\event\PrinterContent";}s:8:"OrderPay";a:1:{i:0;s:28:"addon\printer\event\OrderPay";}s:15:"OrderPayPrinter";a:1:{i:0;s:35:"addon\printer\event\OrderPayPrinter";}s:22:"OrderTakeDeliveryAfter";a:1:{i:0;s:42:"addon\printer\event\OrderTakeDeliveryAfter";}s:24:"OrderTakeDeliveryPrinter";a:1:{i:0;s:44:"addon\printer\event\OrderTakeDeliveryPrinter";}s:22:"MemberRechargeOrderPay";a:1:{i:0;s:42:"addon\printer\event\MemberRechargeOrderPay";}s:29:"MemberRechargeOrderPayPrinter";a:1:{i:0;s:49:"addon\printer\event\MemberRechargeOrderPayPrinter";}}i:44;a:5:{s:7:"OssType";a:1:{i:0;s:25:"addon\qiniu\event\OssType";}s:3:"Put";a:1:{i:0;s:21:"addon\qiniu\event\Put";}s:8:"CloseOss";a:1:{i:0;s:26:"addon\qiniu\event\CloseOss";}s:13:"ClearAlbumPic";a:1:{i:0;s:31:"addon\qiniu\event\ClearAlbumPic";}s:13:"ShowPromotion";a:1:{i:0;s:31:"addon\qiniu\event\ShowPromotion";}}i:45;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:36:"addon\replacebuy\event\ShowPromotion";}s:13:"OrderFromList";a:1:{i:0;s:36:"addon\replacebuy\event\OrderFromList";}}i:46;a:3:{s:13:"ShowPromotion";a:1:{i:0;s:39:"addon\scenefestival\event\ShowPromotion";}s:16:"cronOpenFestival";a:1:{i:0;s:38:"addon\scenefestival\event\OpenFestival";}s:17:"cronCloseFestival";a:1:{i:0;s:39:"addon\scenefestival\event\CloseFestival";}}i:47;a:9:{s:11:"OpenSeckill";a:1:{i:0;s:31:"addon\seckill\event\OpenSeckill";}s:12:"CloseSeckill";a:1:{i:0;s:32:"addon\seckill\event\CloseSeckill";}s:13:"ShowPromotion";a:1:{i:0;s:33:"addon\seckill\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:33:"addon\seckill\event\PromotionType";}s:18:"GoodsPromotionType";a:1:{i:0;s:38:"addon\seckill\event\GoodsPromotionType";}s:14:"GoodsPromotion";a:1:{i:0;s:34:"addon\seckill\event\GoodsPromotion";}s:18:"OrderPromotionType";a:1:{i:0;s:38:"addon\seckill\event\OrderPromotionType";}s:10:"OrderClose";a:1:{i:0;s:30:"addon\seckill\event\OrderClose";}s:19:"PromotionZoneConfig";a:1:{i:0;s:37:"addon\seckill\event\SeckillZoneConfig";}}i:48;a:11:{s:13:"ShowPromotion";a:1:{i:0;s:39:"addon\shopcomponent\event\ShowPromotion";}s:9:"WeappMenu";a:1:{i:0;s:35:"addon\shopcomponent\event\WeappMenu";}s:9:"GoodsEdit";a:1:{i:0;s:35:"addon\shopcomponent\event\GoodsEdit";}s:11:"DeleteGoods";a:1:{i:0;s:37:"addon\shopcomponent\event\DeleteGoods";}s:8:"OrderPay";a:1:{i:0;s:34:"addon\shopcomponent\event\OrderPay";}s:18:"OrderDeliveryAfter";a:1:{i:0;s:44:"addon\shopcomponent\event\OrderDeliveryAfter";}s:22:"OrderTakeDeliveryAfter";a:1:{i:0;s:48:"addon\shopcomponent\event\OrderTakeDeliveryAfter";}s:16:"orderRefundApply";a:1:{i:0;s:42:"addon\shopcomponent\event\OrderRefundApply";}s:18:"RefundStatusChange";a:1:{i:0;s:44:"addon\shopcomponent\event\RefundStatusChange";}s:14:"SyncWxCategory";a:1:{i:0;s:40:"addon\shopcomponent\event\SyncWxCategory";}s:19:"shopcomponentNotify";a:1:{i:0;s:45:"addon\shopcomponent\event\ShopcomponentNotify";}}i:49;a:1:{s:13:"ShowPromotion";a:1:{i:0;s:31:"addon\stock\event\ShowPromotion";}}i:50;a:14:{s:13:"ShowPromotion";a:1:{i:0;s:31:"addon\store\event\ShowPromotion";}s:16:"OrderCreateAfter";a:1:{i:0;s:34:"addon\store\event\OrderCreateAfter";}s:10:"OrderClose";a:1:{i:0;s:28:"addon\store\event\OrderClose";}s:13:"OrderComplete";a:2:{i:0;s:31:"addon\store\event\OrderComplete";i:1;s:47:"addon\store\event\StoreOrderSettlementCalculate";}s:13:"OrderPayAfter";a:1:{i:0;s:31:"addon\store\event\OrderPayAfter";}s:23:"StoreWithdrawPeriodCalc";a:1:{i:0;s:41:"addon\store\event\StoreWithdrawPeriodCalc";}s:8:"AddStore";a:1:{i:0;s:26:"addon\store\event\AddStore";}s:9:"GoodsEdit";a:1:{i:0;s:27:"addon\store\event\GoodsEdit";}s:24:"PointExchangeOrderCreate";a:1:{i:0;s:42:"addon\store\event\PointExchangeOrderCreate";}s:13:"GoodsSkuStock";a:1:{i:0;s:31:"addon\store\event\GoodsSkuStock";}s:19:"OrderGoodsCalculate";a:1:{i:0;s:37:"addon\store\event\OrderGoodsCalculate";}s:21:"OrderCreateCommonData";a:1:{i:0;s:39:"addon\store\event\OrderCreateCommonData";}s:22:"OrderRefundMoneyFinish";a:1:{i:0;s:53:"addon\store\event\StoreOrderRefundSettlementCalculate";}s:17:"PayTransferNotify";a:1:{i:0;s:40:"addon\store\event\WithdrawTransferNotify";}}i:51;a:9:{s:13:"ShowPromotion";a:1:{i:0;s:37:"addon\supermember\event\ShowPromotion";}s:25:"MemberLevelOrderPayNotify";a:1:{i:0;s:49:"addon\supermember\event\MemberLevelOrderPayNotify";}s:8:"OrderPay";a:1:{i:0;s:49:"addon\supermember\event\MemberLevelOrderPayNotify";}s:21:"MemberLevelOrderClose";a:1:{i:0;s:45:"addon\supermember\event\MemberLevelOrderClose";}s:21:"MemberLevelAutoExpire";a:1:{i:0;s:45:"addon\supermember\event\MemberLevelAutoExpire";}s:13:"CouponGetType";a:1:{i:0;s:37:"addon\supermember\event\CouponGetType";}s:16:"IncomeStatistics";a:1:{i:0;s:40:"addon\supermember\event\IncomeStatistics";}s:7:"AddStat";a:1:{i:0;s:31:"addon\supermember\event\AddStat";}s:27:"WapOrderDetailPathByPayInfo";a:1:{i:0;s:51:"addon\supermember\event\WapOrderDetailPathByPayInfo";}}i:52;a:8:{s:9:"OpenTopic";a:1:{i:0;s:27:"addon\topic\event\OpenTopic";}s:10:"CloseTopic";a:1:{i:0;s:28:"addon\topic\event\CloseTopic";}s:13:"ShowPromotion";a:1:{i:0;s:31:"addon\topic\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:31:"addon\topic\event\PromotionType";}s:18:"GoodsPromotionType";a:1:{i:0;s:36:"addon\topic\event\GoodsPromotionType";}s:14:"GoodsPromotion";a:1:{i:0;s:32:"addon\topic\event\GoodsPromotion";}s:18:"OrderPromotionType";a:1:{i:0;s:36:"addon\topic\event\OrderPromotionType";}s:19:"PromotionZoneConfig";a:1:{i:0;s:33:"addon\topic\event\TopicZoneConfig";}}i:53;a:3:{s:13:"ShowPromotion";a:1:{i:0;s:35:"addon\turntable\event\ShowPromotion";}s:13:"PromotionType";a:1:{i:0;s:35:"addon\turntable\event\PromotionType";}s:21:"MemberAccountFromType";a:1:{i:0;s:43:"addon\turntable\event\MemberAccountFromType";}}i:54;a:1:{s:13:"ShowPromotion";a:1:{i:0;s:32:"addon\v3tov4\event\ShowPromotion";}}i:55;a:2:{s:13:"ShowPromotion";a:1:{i:0;s:37:"addon\virtualcard\event\ShowPromotion";}s:10:"GoodsClass";a:1:{i:0;s:34:"addon\virtualcard\event\GoodsClass";}}i:56;a:1:{s:13:"ShowPromotion";a:1:{i:0;s:43:"addon\virtualevaluation\event\ShowPromotion";}}i:57;a:12:{s:6:"Qrcode";a:1:{i:0;s:24:"addon\weapp\event\Qrcode";}s:11:"DecryptData";a:1:{i:0;s:29:"addon\weapp\event\DecryptData";}s:11:"PhoneNumber";a:1:{i:0;s:29:"addon\weapp\event\PhoneNumber";}s:18:"OrderDeliveryAfter";a:1:{i:0;s:36:"addon\weapp\event\OrderDeliveryAfter";}s:31:"OrderDeliveryAfterWeappDelivery";a:1:{i:0;s:49:"addon\weapp\event\OrderDeliveryAfterWeappDelivery";}s:22:"OrderTakeDeliveryAfter";a:1:{i:0;s:40:"addon\weapp\event\OrderTakeDeliveryAfter";}s:16:"BlindboxOrderPay";a:1:{i:0;s:34:"addon\weapp\event\BlindboxOrderPay";}s:16:"GiftCardOrderPay";a:1:{i:0;s:34:"addon\weapp\event\GiftCardOrderPay";}s:22:"MemberRechargeOrderPay";a:1:{i:0;s:40:"addon\weapp\event\MemberRechargeOrderPay";}s:19:"MemberLevelOrderPay";a:1:{i:0;s:37:"addon\weapp\event\MemberLevelOrderPay";}s:21:"PointExchangeOrderPay";a:1:{i:0;s:39:"addon\weapp\event\PointExchangeOrderPay";}s:20:"WeappVirtualDelivery";a:1:{i:0;s:38:"addon\weapp\event\WeappVirtualDelivery";}}i:58;a:1:{s:7:"AddSite";a:1:{i:0;s:32:"addon\wechat\event\AddSiteReplay";}}i:59;a:10:{s:9:"PayNotify";a:1:{i:0;s:31:"addon\wechatpay\event\PayNotify";}s:7:"PayType";a:1:{i:0;s:29:"addon\wechatpay\event\PayType";}s:3:"Pay";a:1:{i:0;s:25:"addon\wechatpay\event\Pay";}s:8:"PayClose";a:1:{i:0;s:30:"addon\wechatpay\event\PayClose";}s:9:"PayRefund";a:1:{i:0;s:31:"addon\wechatpay\event\PayRefund";}s:11:"PayTransfer";a:1:{i:0;s:33:"addon\wechatpay\event\PayTransfer";}s:12:"TransferType";a:1:{i:0;s:34:"addon\wechatpay\event\TransferType";}s:17:"PayTransferResult";a:1:{i:0;s:39:"addon\wechatpay\event\PayTransferResult";}s:11:"AuthcodePay";a:1:{i:0;s:33:"addon\wechatpay\event\AuthcodePay";}s:13:"PayOrderQuery";a:1:{i:0;s:35:"addon\wechatpay\event\PayOrderQuery";}}}