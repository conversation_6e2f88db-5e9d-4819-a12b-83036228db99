<?php /*a:1:{s:46:"E:\aicode\shop\app\shop\view\system\addon.html";i:1741057066;}*/ ?>
<style>
	.item-con { height: auto!important; }
	.item-block-parent .item-block-wrap { align-items: center; }
	.auth-mark{
		display:inline-block;
		margin-left:4px;
		color:red;
		font-size:12px;
	}
</style>

<?php if(!(empty($uninstall) || (($uninstall instanceof \think\Collection || $uninstall instanceof \think\Paginator ) && $uninstall->isEmpty()))): ?>
<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
		<span class="card-title">未安装插件</span>
	</div>
	<div class="layui-card-body layui-field-box">
		<div class="site_list item-block-parent item-five">
			<?php foreach($uninstall as $list_k => $list_v): ?>
			<a class="item-block item-block-hover" href="javascript:;">
				<div class="item-block-wrap">
					<div class="item-pic">
						<?php if(isset($list_v['is_quick']) && $list_v['is_quick'] == 1): ?>
						<img src="https://www.niushop.com/<?php echo htmlentities($list_v['icon']); ?>" />
						<?php else: ?>
						<img src="<?php echo img($list_v['icon']); ?>" />
						<?php endif; ?>
					</div>
					<div class="item-con">
						<div class="item-content-title">
							<?php echo htmlentities($list_v['title']); if($list_v['download'] == 1): ?>
							<div class="auth-mark">未下载</div>
							<?php endif; ?>
						</div>
						<p class="item-content-desc line-hiding" title="<?php echo htmlentities($list_v['description']); ?>"><?php echo htmlentities($list_v['description']); ?></p>
					</div>
				</div>
				<div class="item-float-wrap">
					<div class="item-float">
						<div class="item-float-con">
							<i class="bg-color-red"></i>
							<span>
								<?php if($list_v['download'] == 1): ?>未下载<?php else: ?>未安装<?php endif; ?>
							</span>
						</div>
						<?php if($list_v['download'] == 0): ?>
						<div class="item-float-con" onclick="manage('<?php echo htmlentities($list_v['name']); ?>', 'install')">
							<span>安装</span>
						</div>
						<?php endif; ?>
					</div>
				</div>
			</a>
			<?php endforeach; ?>
		</div>
	</div>
</div>
<?php endif; if(!(empty($addons) || (($addons instanceof \think\Collection || $addons instanceof \think\Paginator ) && $addons->isEmpty()))): ?>
<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
		<span class="card-title">已安装插件</span>
	</div>
	<div class="layui-card-body layui-field-box">
		<div class="site_list item-block-parent item-five">
			<?php foreach($addons as $list_k => $list_v): ?>
			<a class="item-block item-block-hover" href="javascript:;">
				<div class="item-block-wrap">
					<div class="item-pic">
						<img src="<?php echo img($list_v['icon']); ?>" />
					</div>
					<div class="item-con">
						<div class="item-content-title"><?php echo htmlentities($list_v['title']); ?>
						</div>
						<p class="item-content-desc line-hiding" title="<?php echo htmlentities($list_v['description']); ?>"><?php echo htmlentities($list_v['description']); ?></p>
					</div>
				</div>
				<div class="item-float-wrap">
					<div class="item-float">
						<div class="item-float-con">
							<i class="bg-color-blue"></i>
							<span>已安装</span>
						</div>
						<div class="item-float-con" onclick="manage('<?php echo htmlentities($list_v['name']); ?>', 'uninstall')">
							<span>卸载</span>
						</div>
					</div>
				</div>
			</a>
			<?php endforeach; ?> 
		</div>
	</div>
</div>
<?php endif; ?>

<script>
	var repeat_flag = false; //防重复标识
	function manage(addon_name, tag) {
		if (repeat_flag) return;
		repeat_flag = true;
		$.ajax({
			url: ns.url("shop/system/addon"),
			data: {
				addon_name,
				tag
			},
			type: "POST",
			dataType: "JSON",
			success: function(res) {
				layer.msg(res.message);
				if (res.code == 0) {
					location.reload();
				}else{
					repeat_flag = false;
				}
			}
		});
	}

	//查看插件信息
	function addonInfo(name){
		location.hash = ns.hash('shop/upgrade/addonInfo', {name : name});
	}
</script>