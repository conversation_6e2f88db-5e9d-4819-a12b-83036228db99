<?php /*a:1:{s:48:"E:\aicode\shop\app\shop\view\user\user_list.html";i:1741057066;}*/ ?>
<style>
	.margin-left5 {margin-left: 5px;}
	.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
    .store-group-list {line-height: 1.5;}
</style>

<!-- 搜索框 -->
<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加员工</button>

	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_keys" placeholder="请输入员工名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<div class="layui-tab table-tab mt-auto" lay-filter="use_tab">
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="user_list" lay-filter="user_list"></table>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		{{#  if(d.is_admin == 1){ }}
		<span class="cursor margin-left5">系统管理员不可编辑</span>
		{{#  }else if(d.uid == <?php echo htmlentities($user_info['uid']); ?>){ }}
		<a class="layui-btn" lay-event="reset_pass">重置密码</a>
		{{#  }else{ }}
		<a class="layui-btn" lay-event="edit">编辑</a>
		<span>
			<a class="layui-btn" lay-event="delete">删除</a>
		</span>
		<a class="layui-btn" lay-event="reset_pass">重置密码</a>
		{{#  }  }}
	</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{ d.status == 1 ? '正常' : '锁定'}}
</script>

<script>
	var table, form, laytpl, element, layer_pass, repeat_flag = false; //防重复标识
	layui.use(['form', 'laytpl', 'element'], function() {
		form = layui.form;
		laytpl = layui.laytpl;
		element = layui.element;
		form.render();

		table = new Table({
			elem: '#user_list',
			url: ns.url("shop/user/user"),
			cols: [
				[{
					field: 'username',
					title: '员工名称',
					width: '12%',
					unresize: 'false'
				}, {
					field: 'group_name',
					title: '员工角色',
					width: '12%',
					unresize: 'false'
				},
				<?php if($cashier_is_exist): ?>
				{
					title: '门店角色',
					width: '15%',
					unresize: 'false',
					templet: function (data) {
						if(data.is_admin == 1){
							var h = '';
						}else {
							var h = '<div class="store-group-list">';
							data.user_group_list.forEach(function (item) {
								h += `<div class="store-group" title="${item.store_name}：${item.group_name}">${item.store_name}：${item.group_name}</div>`
							})
							h += '</div>';
						}
						return h;
					}
				},
				<?php endif; ?>
				{
					field: 'login_ip',
					title: '最后登录IP',
					unresize: 'false'
				}, {
					field: 'login_time',
					title: '最后登录时间',
					width: '15%',
					unresize: 'false',
					templet: function(data) {
						return ns.time_to_date(data.login_time); //创建时间转换方法
					}
				}, {
					field: 'status',
					title: '员工状态',
					width: '8%',
					unresize: 'false',
					templet: '#status'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]
		});

		element.on('tab(use_tab)', function(){
		    table.reload({
		        page: {curr: 1},
		        where:{'status':this.getAttribute('lay-id')}
		    });
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.hash = ns.hash("shop/user/editUser", {"uid": data.uid});
					break;
				case 'delete': //删除
					deleteUser(data.uid);
					break;
				case 'reset_pass': //重置密码
					resetPassword(data);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteUser(uid) {
			if (repeat_flag) return false;
			repeat_flag = true;
			
			layer.confirm('确定要删除该员工吗?', function(index) {
				layer.close(index);
				$.ajax({
					url: ns.url("shop/user/deleteUser"),
					data: {uid},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						repeat_flag = false;
						deleteUserLoginInfo();
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		function deleteUserLoginInfo() {
			if (repeat_flag) return false;
			repeat_flag = true;

			layer.confirm('是否清空所有员工登录信息，清空后所有员工都将被迫退出重新登录', { btn: ['是']},function(index) {
				layer.close(index);
				$.ajax({
					url: ns.url("shop/user/deleteUserLoginInfo"),
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							setTimeout(function(){
								location.reload(); // 刷新页面
							},500);
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}
		
		/**
		 * 重置密码
		 */
		function resetPassword(data) {
			laytpl($("#pass_change").html()).render(data, function(html) {
				layer_pass = layer.open({
					title: '重置密码',
					skin: 'layer-tips-class',
					type: 1,
					area: ['500px'],
					content: html,
				});
			});
		}
		
		form.on('submit(repass)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				type: "POST",
				url: ns.url("shop/user/modifyPassword"),
				data: data.field,
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;
		
					if (res.code == 0) {
						layer.closeAll('page');
					}
				}
			});
		});
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data){
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
		
		/**
		 * 表单验证
		 */
		form.verify({
			repass: function(value) {
				if (value != $("input[name='password']").val()) {
					return "输入错误,两次密码不一致!";
				}
			}
		});
	});

	function add() {
		location.hash = ns.hash("shop/user/addUser");
	}
	
	function closePass() {
		layer.close(layer_pass);
	}
</script>

<!-- 重置密码弹框html -->
<script type="text/html" id="pass_change">
	<div class="layui-form" id="reset_pass">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>新密码</label>
			<div class="layui-input-block">
				<input type="password" id="new_pass" name="password" placeholder="请输入密码" class="layui-input len-mid" lay-verify="required"  maxlength="18" autocomplete="off">
			</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>确认新密码</label>
			<div class="layui-input-block">
				<input type="password" name="password" placeholder="请输入密码" lay-verify="repass" class="layui-input len-mid" maxlength="18" autocomplete="off">
			</div>
			<div class="word-aux mid">
				<p>请再一次输入密码，两次输入密码须一致</p>
			</div>
		</div>
		
		<div class="form-row mid">
			<button class="layui-btn" lay-submit lay-filter="repass">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
		</div>

		<input class="reset-pass-id" type="hidden" name="uid" value="{{d.uid}}"/>
	</div>
</script>