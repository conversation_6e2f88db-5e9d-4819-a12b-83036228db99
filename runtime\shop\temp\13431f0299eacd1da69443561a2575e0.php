<?php /*a:1:{s:45:"E:\aicode\shop\app\shop\view\shop\config.html";i:1741057066;}*/ ?>
<style type="text/css">
	.examples {cursor: pointer;}
	.label-width {width: 80px; }
</style>

<div class="layui-form">
	<div class="layui-form-item">
		<label class="layui-form-label">网站名称：</label>
		<div class="layui-input-block">
			<input type="text" name="site_name" autocomplete="off" value="<?php echo htmlentities($shop_info['site_name']); ?>" class="layui-input len-long">
		</div>
		<div class="word-aux"><p>说明：该标题在管理端头部标题展示，形式为菜单名-网站名称。</p></div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">网站logo：</label>
		<div class="layui-input-block img-upload">
			<div class="upload-img-block">
				<div class="upload-img-box <?php if(!empty($shop_info['logo'])): ?>hover<?php endif; ?> ">
					<div class="upload-default" id="logoUpload">
						<?php if(!empty($shop_info['logo'])): ?>
							<div id="preview_logoUpload" class="preview_img">
								<img layer-src src="<?php echo img($shop_info['logo']); ?>" class="img_prev"/>
							</div>
						<?php else: ?>
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
						<?php endif; ?>
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="logo" value="<?php echo isset($shop_info['logo']) ? htmlentities($shop_info['logo']) : ''; ?>"/>
				</div>
				<!-- <p id="logoUpload" class=" <?php if($shop_info['logo']): ?> replace <?php else: ?> no-replace<?php endif; ?>">替换</p>
				<input type="hidden" name="logo" value="<?php echo htmlentities($shop_info['logo']); ?>"/>
				<i class="del <?php if($shop_info['logo']): ?>show<?php endif; ?>">x</i> -->
			</div>
		</div>

		<div class="word-aux">
			<p>建议图片尺寸：200*60像素；图片格式：jpg、png、jpeg</p>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">方形logo：</label>
		<div class="layui-input-block img-upload">
			<div class="upload-img-block">
				<div class="upload-img-box <?php if(!empty($shop_info['logo_square'])): ?>hover<?php endif; ?> ">
					<div class="upload-default" id="logo_squareUpload">
						<?php if(!empty($shop_info['logo_square'])): ?>
							<div id="preview_logo_squareUpload" class="preview_img">
								<img layer-src src="<?php echo img($shop_info['logo_square']); ?>" class="img_prev"/>
							</div>
						<?php else: ?>
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
						<?php endif; ?>
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="logo_square" value="<?php echo isset($shop_info['logo_square']) ? htmlentities($shop_info['logo_square']) : ''; ?>"/>
				</div>
			</div>
		</div>

		<div class="word-aux">
			<p>建议图片尺寸：80*80像素；图片格式：jpg、png、jpeg。用于分享使用。</p>
		</div>
	</div>

	<!--<div class="layui-form-item">-->
		<!--<label class="layui-form-label">店铺头像：</label>-->
		<!--<div class="layui-input-block img-upload">-->
			<!--<div class="upload-img-block square">-->
				<!--<div class="upload-img-box" id="avatarUpload">-->
					<!--<?php if($shop_info['avatar']): ?>-->
						<!--<img src="<?php echo img($shop_info['avatar']); ?>" />-->
					<!--<?php else: ?>-->
						<!--<div class="upload-default">-->
<!--							<i class="iconfont iconshangchuan"></i>-->
							<!--<p>点击上传</p>-->
						<!--</div>-->
					<!--<?php endif; ?>-->
				<!--</div>-->
			<!--</div>-->
			<!--<input type="hidden" name="banner" value="<?php echo htmlentities($shop_info['banner']); ?>"/>-->
		<!--</div>-->

		<!--<div class="word-aux">-->
			<!--<p>建议图片尺寸：800*800像素；图片格式：jpg、png、jpeg。</p>-->
		<!--</div>-->
	<!--</div>-->

	<!--<div class="layui-form-item">-->
		<!--<label class="layui-form-label">店铺大图：</label>-->
		<!--<div class="layui-input-block img-upload">-->
			<!--<div class="upload-img-block">-->
				<!--<div class="upload-img-box" id="bannerUpload">-->
					<!--<?php if($shop_info['avatar']): ?>-->
						<!--<img src="<?php echo img($shop_info['banner']); ?>" />-->
					<!--<?php else: ?>-->
						<!--<div class="upload-default">-->
<!--							<i class="iconfont iconshangchuan"></i>-->
							<!--<p>点击上传</p>-->
						<!--</div>-->
					<!--<?php endif; ?>-->
				<!--</div>-->
			<!--</div>-->
			<!--<input type="hidden" name="avatar" value="<?php echo htmlentities($shop_info['avatar']); ?>"/>-->
		<!--</div>-->
		<!--<div class="word-aux">-->
			<!--<p>建议图片高度：150像素；图片格式：jpg、png、jpeg。</p>-->
		<!--</div>-->
	<!--</div>-->

	<div class="layui-form-item">
		<label class="layui-form-label">网站标题：</label>
		<div class="layui-input-block">
			<input type="text" name="seo_title" autocomplete="off" value="<?php echo isset($shop_info['seo_title']) ? htmlentities($shop_info['seo_title']) : ''; ?>" class="layui-input len-long">
		</div>
		<div class="word-aux"><p>说明：该标题仅在PC端首页标题展示</p></div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">网站关键字：</label>
		<div class="layui-input-block">
			<input type="text" name="seo_keywords" autocomplete="off" value="<?php echo htmlentities($shop_info['seo_keywords']); ?>" class="layui-input len-long">
		</div>
		<div class="word-aux"><p>多个关键字之间用英文“,”隔开</p></div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">网站描述：</label>
		<div class="layui-input-block">
			<textarea name="seo_description" class="layui-textarea len-long" maxlength="150"><?php echo htmlentities($shop_info['seo_description']); ?></textarea>
		</div>
	</div>

    <div class="layui-form-item">
	   <label class="layui-form-label">服务电话：</label>
	   <div class="layui-input-block">
		   <input type="text" name="site_tel" autocomplete="off" value="<?php echo htmlentities($shop_info['site_tel']); ?>" class="layui-input len-long">
	   </div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">商城状态：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline" style="float: none;display: block;">
				<label class="layui-form-label label-width">pc端：</label>
				<input type="radio" name="shop_pc_status" value="1" title="开启" <?php if(isset($shop_status['shop_pc_status']) && $shop_status['shop_pc_status'] == 1): ?> checked <?php endif; ?>>
				<input type="radio" name="shop_pc_status" value="0" title="关闭" <?php if(isset($shop_status['shop_pc_status']) && $shop_status['shop_pc_status'] == 0): ?> checked <?php endif; ?>>
			</div>
			<div class="layui-input-inline" style="float: none;display: block;">
				<label class="layui-form-label label-width">h5端：</label>
				<input type="radio" name="shop_h5_status" value="1" title="开启" <?php if(isset($shop_status['shop_h5_status']) && $shop_status['shop_h5_status'] == 1): ?> checked <?php endif; ?>>
				<input type="radio" name="shop_h5_status" value="0" title="关闭" <?php if(isset($shop_status['shop_h5_status']) && $shop_status['shop_h5_status'] == 0): ?> checked <?php endif; ?>>
			</div>
			<div class="layui-input-inline" style="float: none;display: block;">
				<label class="layui-form-label label-width">小程序端：</label>
				<input type="radio" name="shop_weapp_status" value="1" title="开启" <?php if(isset($shop_status['shop_weapp_status']) && $shop_status['shop_weapp_status'] == 1): ?> checked <?php endif; ?>>
				<input type="radio" name="shop_weapp_status" value="0" title="关闭" <?php if(isset($shop_status['shop_weapp_status']) && $shop_status['shop_weapp_status'] == 0): ?> checked <?php endif; ?>>
			</div>
		</div>
		<div class="word-aux">
			<p>商城状态为关闭时，用户将无法访问商城。<a onclick="showDemo()" class="examples text-color">查看示例</a></p>
		</div>
	</div>

	<!--<div class="layui-form-item">-->
		<!--<label class="layui-form-label">工作日：</label>-->
		<!--<div class="layui-input-block" id="work_week">-->
			<!--<input type="checkbox" name="work_week1" value="1" title="周一" lay-skin="primary">-->
			<!--<input type="checkbox" name="work_week2" value="2" title="周二" lay-skin="primary">-->
			<!--<input type="checkbox" name="work_week3" value="3" title="周三" lay-skin="primary">-->
			<!--<input type="checkbox" name="work_week4" value="4" title="周四" lay-skin="primary">-->
			<!--<input type="checkbox" name="work_week5" value="5" title="周五" lay-skin="primary">-->
			<!--<input type="checkbox" name="work_week6" value="6" title="周六" lay-skin="primary">-->
			<!--<input type="checkbox" name="work_week7" value="7" title="周日" lay-skin="primary">-->
		<!--</div>-->
	<!--</div>-->
	<!--<div class="layui-form-item">-->
	<!--<div class="layui-inline">-->
		<!--<label class="layui-form-label">营业时间：</label>-->
		<!--<div class="layui-input-inline">-->
			<!--<input type="text" class="layui-input" id="start_time" placeholder="开始时间" autocomplete="off" value="<?php if($shop_info['start_time']): ?> <?php echo htmlentities($shop_info['start_time']); ?> <?php endif; ?>" readonly>-->
			<!--<i class=" iconrili iconfont calendar"></i>-->
			<!--<input type="hidden" class="layui-input start-time" name="start_time">-->
		<!--</div>-->
		<!--<div class="layui-input-inline">-</div>-->
		<!--<div class="layui-input-inline">-->
			<!--<input type="text" class="layui-input" id="end_time" placeholder="结束时间" autocomplete="off" value="<?php if($shop_info['end_time']): ?> <?php echo htmlentities($shop_info['end_time']); ?> <?php endif; ?>" readonly>-->
			<!--<i class=" iconrili iconfont calendar"></i>-->
			<!--<input type="hidden" class="layui-input end-time" name="end_time">-->
		<!--</div>-->
	<!--</div>-->
	<!--</div>-->

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
	</div>
	<a id="btom_two"></a>
	<a id="btom_three"></a>
</div>

<script>
	/**
	 * 初始化营业时间
	 */
	var startTime = "<?php echo htmlentities($shop_info['start_time']); ?>",
		endTime = "<?php echo htmlentities($shop_info['end_time']); ?>";
	var saveData = null;
	var totalUploadNum = 0;
	var completeUploadNum = 0;
	var logo_upload,logo_square_upload;
	if (Number(startTime)){
		$("#start_time").val(ns.time_to_date(startTime, "H:i:s"));
		$(".start-time").val(startTime);
	}

	if (Number(endTime)){
		$("#end_time").val(ns.time_to_date(endTime, "H:i:s"));
		$(".end-time").val(endTime);
	}

	layui.use(['form', 'laydate'], function(){
		var form = layui.form,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识
			
		form.render();

		//初始化工作日
		var workWeek = "<?php echo htmlentities($shop_info['work_week']); ?>",
				workArr = workWeek.split(",");

		for (var i = 0; i < workArr.length; i++){
			$("input[name=work_week"+ workArr[i] +"]").prop("checked",true);
		}

		//获取 - 开始时间
		var start_hours, start_minutes, start_seconds;
		laydate.render({
			elem: '#start_time',
			type: 'time',
			done: function(value, date){
				start_hours = date.hours;
				start_minutes = date.minutes;
				start_seconds = date.seconds;
				$(".start-time").val(ns.date_to_time(date.year + "-" + date.month + "-" + date.date + " " + date.hours + ":" + date.minutes + ":" + date.seconds))
			}
		});

		//获取 - 结束时间
		laydate.render({
			elem: '#end_time',
			type: 'time',
			done: function(value, date){
				$(".end-time").val(ns.date_to_time(date.year + "-" + date.month + "-" + date.date + " " + date.hours + ":" + date.minutes + ":" + date.seconds))
			}
		});

		form.render();

		form.on('submit(save)', function(data){
			saveData = data;
			var obj = $("img.img_prev[data-prev='1']");
			totalUploadNum = obj.length;

			if(totalUploadNum > 0){
				obj.each(function(){
					var actionId = $(this).attr('data-action-id');
					$(actionId).click();
				})
			}else{
				saveFunc();
			}
		});

		// 店铺LOGO
		logo_upload = new Upload({
			elem: '#logoUpload'
			,auto: false //选择文件后不自动上传
			,bindAction: '#btom_two' //指向一个按钮触发上传
			,callback: function (res) {
				if(res.code >= 0){
					//验证，增加是否上传完成
					completeUploadNum += 1;
					saveData.field.logo = res.data.pic_path;
					if(completeUploadNum == totalUploadNum){
						saveFunc();
					}
				}
			}
		});

		// 店铺方形LOGO
		logo_square_upload = new Upload({
			elem: '#logo_squareUpload'
			,auto: false //选择文件后不自动上传
			,bindAction: '#btom_three' //指向一个按钮触发上传
			,callback: function (res) {
				if(res.code >= 0){
					//验证，增加是否上传完成
					completeUploadNum += 1;
					saveData.field.logo_square = res.data.pic_path;
					if(completeUploadNum == totalUploadNum){
						saveFunc();
					}
				}
			}
		});

		// 店铺头像
		// var avatar_upload = new Upload({
		// 	elem: '#avatarUpload'
		// });
		//
		// // 店铺大图
		// var banner_upload = new Upload({
		// 	elem: '#bannerUpload'
		// });
		function saveFunc(){
			var data = saveData;
			//工作日
			var week_arr = [];
			$("#work_week input").each(function () {
				if ($(this).is(":checked")) {
					week_arr.push($(this).val());
				}
			});

			data.field.work_week = week_arr.toString();

			// 删除图片
			if(!data.field.logo) logo_upload.delete();
			if(!data.field.logo_square) logo_square_upload.delete();

			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				type: 'POST',
				url: ns.url("shop/shop/config"),
				data: data.field,
				dataType: 'JSON',
				sync: false,
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;
				}
			});
		}

	});
	
	$("input[name='domain_name']").change(function(){
		var domain_name = $("input[name='domain_name']").val();
		$.ajax({
			type: 'POST',
			url: ns.url("shop/config/h5DomainName"),
			data:{
				"domain_name" : domain_name
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
			}
		})
	})

	function showDemo(){
	    layer.open({
	        title: '查看示例',
	        type: 1,
	        area: ['500px', '660px'],
	        content: '<img style="margin:20px 80px;" src="http://**********/app/shop/view/public/img/dayang.png">'
	    })
	}

</script>
