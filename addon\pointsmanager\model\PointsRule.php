<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\pointsmanager\model;

use app\model\BaseModel;

/**
 * 积分规则模型
 */
class PointsRule extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'points_rule';

    /**
     * 添加积分规则
     * @param array $data
     * @return array
     */
    public function addRule($data)
    {
        try {
            $data['create_time'] = time();
            $data['update_time'] = time();
            $result = $this->save($data);
            
            if ($result) {
                return $this->success($result);
            } else {
                return $this->error();
            }
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 编辑积分规则
     * @param int $id
     * @param array $data
     * @return array
     */
    public function editRule($id, $data)
    {
        try {
            $data['update_time'] = time();
            $result = $this->where('id', $id)->update($data);
            
            if ($result !== false) {
                return $this->success($result);
            } else {
                return $this->error();
            }
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 删除积分规则
     * @param int $id
     * @return array
     */
    public function deleteRule($id)
    {
        try {
            $result = $this->where('id', $id)->delete();
            
            if ($result) {
                return $this->success($result);
            } else {
                return $this->error();
            }
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 获取积分规则列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @return array
     */
    public function getRuleList($condition = [], $field = '*', $order = 'create_time desc')
    {
        try {
            $list = $this->field($field)
                ->where($condition)
                ->order($order)
                ->select()
                ->toArray();
            
            foreach ($list as &$item) {
                $item['status_text'] = $item['status'] == 1 ? '启用' : '禁用';
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_text'] = date('Y-m-d H:i:s', $item['update_time']);
            }
            
            return $this->success($list);
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 获取积分规则详情
     * @param int $id
     * @return array
     */
    public function getRuleInfo($id)
    {
        try {
            $info = $this->where('id', $id)->find();
            
            if ($info) {
                $info = $info->toArray();
                $info['status_text'] = $info['status'] == 1 ? '启用' : '禁用';
                $info['create_time_text'] = date('Y-m-d H:i:s', $info['create_time']);
                $info['update_time_text'] = date('Y-m-d H:i:s', $info['update_time']);
                
                return $this->success($info);
            } else {
                return $this->error('', '规则不存在');
            }
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 根据代码获取规则
     * @param string $code
     * @return array
     */
    public function getRuleByCode($code)
    {
        try {
            $info = $this->where('code', $code)
                ->where('status', 1)
                ->find();
            
            if ($info) {
                return $this->success($info->toArray());
            } else {
                return $this->error('', '规则不存在或已禁用');
            }
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 更新规则状态
     * @param int $id
     * @param int $status
     * @return array
     */
    public function updateStatus($id, $status)
    {
        try {
            $result = $this->where('id', $id)->update([
                'status' => $status,
                'update_time' => time()
            ]);
            
            if ($result !== false) {
                return $this->success($result);
            } else {
                return $this->error();
            }
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }
}
