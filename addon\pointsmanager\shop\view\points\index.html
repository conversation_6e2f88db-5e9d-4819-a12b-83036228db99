<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>积分管理</title>
    <link rel="stylesheet" href="__STATIC__/ext/layui/css/layui.css">
    <style>
        .statistics-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="layui-container" style="margin-top: 20px;">
        <div class="layui-row">
            <div class="layui-col-md12">
                <h2>积分管理概览</h2>
            </div>
        </div>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="statistics-card">
                    <div class="stat-item">
                        <div class="stat-number" id="totalAdd">0</div>
                        <div class="stat-label">总积分发放</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="statistics-card">
                    <div class="stat-item">
                        <div class="stat-number" id="totalReduce">0</div>
                        <div class="stat-label">总积分消耗</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="statistics-card">
                    <div class="stat-item">
                        <div class="stat-number" id="todayAdd">0</div>
                        <div class="stat-label">今日发放</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="statistics-card">
                    <div class="stat-item">
                        <div class="stat-number" id="monthAdd">0</div>
                        <div class="stat-label">本月发放</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row" style="margin-top: 30px;">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>快捷操作</h3>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-btn-group">
                            <button class="layui-btn" onclick="location.href='pointsmanager://shop/points/records'">
                                <i class="layui-icon layui-icon-list"></i> 积分记录
                            </button>
                            <button class="layui-btn layui-btn-normal" onclick="location.href='pointsmanager://shop/points/rules'">
                                <i class="layui-icon layui-icon-set"></i> 积分规则
                            </button>
                            <button class="layui-btn layui-btn-warm" onclick="location.href='pointsmanager://shop/points/statistics'">
                                <i class="layui-icon layui-icon-chart"></i> 积分统计
                            </button>
                            <button class="layui-btn layui-btn-danger" onclick="addPoints()">
                                <i class="layui-icon layui-icon-add-1"></i> 手动添加积分
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            
            // 加载统计数据
            loadStatistics();
        });
        
        function loadStatistics() {
            $.ajax({
                url: '',
                type: 'POST',
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0) {
                        $('#totalAdd').text(res.data.total_add);
                        $('#totalReduce').text(res.data.total_reduce);
                        $('#todayAdd').text(res.data.today_add);
                        $('#monthAdd').text(res.data.month_add);
                    }
                }
            });
        }
        
        function addPoints() {
            layer.open({
                type: 2,
                title: '手动添加积分',
                area: ['600px', '400px'],
                content: 'pointsmanager://shop/points/addPoints'
            });
        }
    </script>
</body>
</html>
