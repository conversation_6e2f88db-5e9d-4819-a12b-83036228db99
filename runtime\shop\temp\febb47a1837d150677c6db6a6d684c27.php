<?php /*a:5:{s:53:"E:\aicode\shop\app\shop\view\virtualorder\detail.html";i:1741057066;s:59:"E:\aicode\shop\app\shop\view\order\order_common_action.html";i:1741057066;s:43:"app/shop/view/order/order_adjust_price.html";i:1741057066;s:76:"E:\aicode\shop\app\shop\view\virtualorder\virtual_order_delivery_action.html";i:1741057066;s:58:"E:\aicode\shop\app\shop\view\order\shop_active_refund.html";i:1741057066;}*/ ?>
<link rel="stylesheet" href="http://**********/app/shop/view/public/css/order_detail.css"/>

<!-- 订单详情、订单状态 -->
<div class="order-detail layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">订单详情</span>
    </div>
	<div class="order-information order-information-bottom layui-card-body">
		<div class="order-information-contentOne">
			<div class="contentOne-content">
				<div class="contentOne-content-title">交易流水号：</div>
				<div class="contentOne-content-text text-num"><?php echo htmlentities($order_detail['out_trade_no']); ?></div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">订单编号：</div>
				<div class="contentOne-content-text"><?php echo htmlentities($order_detail['order_no']); ?></div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">订单类型：</div>
				<div class="contentOne-content-text"><?php echo htmlentities($order_detail['order_type_name']); ?></div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">订单来源：</div>
				<div class="contentOne-content-text"><?php echo htmlentities($order_detail['order_from_name']); ?></div>
			</div>
			<?php if($order_detail['pay_status'] == 1): ?>
			<div class="contentOne-content">
				<div class="contentOne-content-title">付款方式：</div>
				<div class="contentOne-content-text"><?php echo htmlentities($order_detail['pay_type_name']); ?></div>
			</div>
			<?php endif; ?>
			<div class="contentOne-content">
				<div class="contentOne-content-title">买家：</div>
				<div class="contentOne-content-text"><a class="text-color" href="javascript:toMemberDetail();"><?php echo htmlentities($order_detail['nickname']); ?></a></div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">手机号：</div>
				<div class="contentOne-content-text"><?php echo htmlentities($order_detail['mobile']); ?></div>
			</div>
		</div>
		<div class="order-information-contentTwo">
			<div class="contentOne-content">
				<div class="contentOne-content-title">营销活动：</div>
				<div class="contentOne-content-text"><?php if(empty($order_detail['promotion_type_name'])): ?>-<?php else: ?><?php echo htmlentities($order_detail['promotion_type_name']); ?><?php endif; ?></div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">买家留言：</div>
				<div class="contentOne-content-text contentOne-content-text-die">
					<?php if($order_detail['buyer_message'] == ""): ?>
					-
					<?php else: ?>
					<?php echo htmlentities($order_detail['buyer_message']); ?>
					<?php endif; ?>
				</div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">备注：</div>
				<div class="contentOne-content-text">
					<?php if($order_detail['remark'] == ""): ?>
					-
					<?php else: ?>
					<?php echo htmlentities($order_detail['remark']); ?>
					<?php endif; ?>
				</div>
			</div>
			<div class="contentOne-content">
                <div class="contentOne-content-title">操作人：核销员</div>
                <div class="contentOne-content-text">
                    <?php if($order_detail['verifier_name'] == ""): ?>
                    -
                    <?php else: ?>
                    <?php echo htmlentities($order_detail['verifier_name']); ?>
                    <?php endif; ?>
                </div>
            </div>
		</div>
	</div>
	
    <div class="layui-card-header">
        <span class="card-title">订单状态</span>
    </div>
	<div class="order-information-contentOne order-orderStatus-contentOne  layui-card-body">
		<div class="contentOne-content">
			<div class="contentOne-content-title">订单状态：</div>
			<div class="contentOne-content-text contentOne-content-textNew"><?php echo htmlentities($order_detail['order_status_name']); ?></div>
		</div>
		<div class="contentTow-operation">
			<div class="contentTow-operation-content bg-color-light-9 contentTow-operation-new" onclick="orderRemark('<?php echo htmlentities($order_detail['order_id']); ?>')">备注</div>
			<?php 
			$order_json_data = json_decode($order_detail['order_status_action'], true);
			$action = $order_json_data['action'];
			 foreach($action as $action_k => $action_item): ?>
				<a class="contentTow-operation-content bg-color-light-9 contentTow-operation-new" href="javascript:orderAction('<?php echo htmlentities($action_item['action']); ?>', '<?php echo htmlentities($order_detail['order_id']); ?>')"><?php echo htmlentities($action_item['title']); ?></a>
			<?php endforeach; if(addon_is_exit("printer") && $order_detail['order_status'] != -1): ?>
			<a class="contentTow-operation-content bg-color-light-9 contentTow-operation-new bg-color-light-9" href="javascript:printTicket('<?php echo htmlentities($order_detail['order_id']); ?>');" >打印小票</a>
			<?php endif; ?>
		</div>
		<div class="orderStatus">
			<div class="orderStatus-content-title">提醒：</div>
			<div class="orderStatus-content-text">
				<p class="text-tile">买家付款成功后，货款将直接进入您的商户号（微信、支付宝）</p>
			</div>
		</div>
	</div>

	<?php if(isset($order_detail['form'])): ?>
    	<!-- 表单信息 -->
		<div class="formFields">
            <div class="layui-card-header">
                <span class="card-title">表单信息</span>
            </div>
			<div class="order-information order-formFields layui-card-body">
				<?php if(is_array($order_detail['form']) || $order_detail['form'] instanceof \think\Collection || $order_detail['form'] instanceof \think\Paginator): if( count($order_detail['form'])==0 ) : echo "" ;else: foreach($order_detail['form'] as $key=>$vo): ?>
				<div class="contentOne-content">
					<div class="contentOne-content-title"><?php if(is_array($vo['value'])): ?><?php echo htmlentities($vo['value']['title']); ?><?php endif; ?>：</div>
					<div class="contentOne-content-text">
					<?php if(isset($vo['controller']) && $vo['controller'] == 'Img'): if(is_array($vo['img_lists']) || $vo['img_lists'] instanceof \think\Collection || $vo['img_lists'] instanceof \think\Paginator): if( count($vo['img_lists'])==0 ) : echo "" ;else: foreach($vo['img_lists'] as $key=>$io): ?>
                            <div class="form-img">
                                <div class="form-img-wrap">
                                    <img src="<?php echo img($io); ?>" layer-src>
                                </div>  
                            </div>
                        <?php endforeach; endif; else: echo "" ;endif; else: if(isset($vo['val'])): ?>
                    	<?php echo htmlentities($vo['val']); ?>
						<?php endif; ?>
                    <?php endif; ?>
                    </div>
				</div>
				<?php endforeach; endif; else: echo "" ;endif; ?>
			</div>
		</div>
	<?php endif; if($order_detail['is_invoice'] == 1): ?>
		<!-- 发票信息 -->
		<div class="formFields">
            <div class="layui-card-header">
                <span class="card-title">发票信息</span>
            </div>
			<div class="order-information order-formFields layui-card-body">
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票类型：</div>
					<div class="contentOne-content-text"><?php if($order_detail['invoice_type'] == 1): ?>纸质<?php else: ?>电子<?php endif; if($order_detail['is_tax_invoice'] == 1): ?>专票<?php else: ?>普票<?php endif; ?></div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票抬头：</div>
					<div class="contentOne-content-text"><?php echo htmlentities($order_detail['invoice_title']); ?></div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票抬头类型：</div>
					<div class="contentOne-content-text"><?php echo $order_detail['invoice_title_type']==1 ? '个人'  :  '企业'; ?></div>
				</div>
				<?php if($order_detail['invoice_title_type'] == 2): ?>
				<div class="contentOne-content">
					<div class="contentOne-content-title">纳税人识别号：</div>
					<div class="contentOne-content-text"><?php echo htmlentities($order_detail['taxpayer_number']); ?></div>
				</div>
				<?php endif; ?>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票内容：</div>
					<div class="contentOne-content-text"><?php echo htmlentities($order_detail['invoice_content']); ?></div>
				</div>
				<?php if($order_detail['invoice_type'] == 1): ?>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票邮寄地址：</div>
					<div class="contentOne-content-text"><?php echo htmlentities($order_detail['invoice_full_address']); ?></div>
				</div>
				<?php else: ?>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票接收邮件：</div>
					<div class="contentOne-content-text"><?php echo htmlentities($order_detail['invoice_email']); ?></div>
				</div>
				<?php endif; ?>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票费用：</div>
					<div class="contentOne-content-text">￥<?php echo htmlentities($order_detail['invoice_money']); ?></div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票税率：</div>
					<div class="contentOne-content-text"><?php echo htmlentities($order_detail['invoice_rate']); ?>%</div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票邮寄费用：</div>
					<div class="contentOne-content-text">￥<?php echo htmlentities($order_detail['invoice_delivery_money']); ?></div>
				</div>
			</div>
		</div>
	<?php endif; ?>
</div>

<!-- 商品信息、订单操作日志 -->
<div class="shop-information layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">商品信息</span>
    </div>
	<div class="shop-information-table layui-card-body">
		<table lay-filter="parse-table-order-product" lay-skin="line">
			<thead>
				<tr class="table-trOne">
					<th lay-data="{field:'product_name', width:200}">商品</th>
					<th lay-data="{field:'price'}">价格</th>
					<th lay-data="{field:'sale_num'}">数量</th>
					<th lay-data="{field:'total_money'}">小计（元）</th>
					<th lay-data="{field:'refund_status'}">退款状态</th>
					<?php if(isset($order_detail['virtual_goods'])): if($order_detail['goods_class'] == 2): ?>
						<th style="text-align:center">总核销次数</th>
						<th style="text-align:center">剩余次数</th>
						<th style="text-align:center">核销码</th>
						<th>有效期</th>
						<th>操作</th>
						<?php endif; if($order_detail['goods_class'] == 3): ?>
						<th>卡密信息</th>
						<?php endif; if($order_detail['goods_class'] == 4): ?>
							<th style="text-align:center">总核销次数</th>
							<th style="text-align:center">剩余次数</th>
							<th style="text-align:center">核销码</th>
							<th>有效期</th>
							<th>操作</th>
						<?php endif; ?>
					<?php endif; ?>
				</tr>
			</thead>
			<tbody>
				<?php foreach($order_detail['order_goods'] as $list_k => $order_goods_item): ?>
				<tr class="table-trTow">
					<td><?php echo htmlentities($order_goods_item['sku_name']); ?></td>
					<td><?php echo htmlentities($order_goods_item['price']); ?></td>
					<td><?php echo htmlentities($order_goods_item['num']); ?></td>
					<td><?php echo htmlentities($order_goods_item['goods_money']); ?></td>
					<td>
						<?php if($order_goods_item['refund_status'] != 0): ?>
						<div><a class="" href='<?php echo href_url("shop/orderrefund/detail?order_goods_id=".$order_goods_item["order_goods_id"]); ?>'><?php echo htmlentities($order_goods_item['refund_status_name']); ?></a></div>
						<?php 
							$refund_money = $order_goods_item['shop_active_refund_money']+$order_goods_item['refund_real_money'];
							$refund_money = sprintf("%.2f",$refund_money);
						 if($refund_money > 0): ?>
						<div style="color:red;">￥<?php echo htmlentities($refund_money); ?></div>
						<?php endif; elseif($order_detail['is_enable_refund'] == 1 && $order_detail['promotion_type'] != 'blindbox' && $order_goods_item['shop_active_refund'] == 0 && $order_goods_item['real_goods_money'] > 0): ?>
						<div><a class="text-color" href="javascript:;" style="border:1px solid;padding:2px;" onclick="shopActiveRefund('<?php echo htmlentities($order_goods_item['order_goods_id']); ?>')" >主动退款</a></div>
						<?php endif; ?>
					</td>
					<?php if(isset($order_detail['virtual_goods'])): if($order_detail['goods_class'] == 2): ?>
						<td style="text-align:center;"><?php echo htmlentities($order_detail['virtual_goods']['verify_total_count']); ?></td>
						<td style="text-align:center;"><?php echo htmlentities($order_detail['virtual_goods']['verify_total_count'] - $order_detail['virtual_goods']['verify_use_num']); ?></td>
						<td style="text-align:center;"><?php echo htmlentities($order_detail['virtual_code']); ?></td>
						<td>
							<?php if($order_detail['virtual_goods']['expire_time'] > 0): ?><?php echo time_to_date($order_detail['virtual_goods']['expire_time']); else: ?>永久有效<?php endif; ?></td>
						<td><a href="javascript:;" class="text-color" onclick="showVerifyRecord()">核销记录</a></td>
						<?php endif; if($order_detail['goods_class'] == 3): ?>
						<td>
							<div class="carmichael">
								<?php if(is_array($order_detail['virtual_goods']) || $order_detail['virtual_goods'] instanceof \think\Collection || $order_detail['virtual_goods'] instanceof \think\Paginator): if( count($order_detail['virtual_goods'])==0 ) : echo "" ;else: foreach($order_detail['virtual_goods'] as $key=>$vo): ?>
								<div>卡号：<?php echo htmlentities($vo['card_info']['cardno']); ?><br>密码：<?php echo htmlentities($vo['card_info']['password']); ?></div>
								<?php endforeach; endif; else: echo "" ;endif; ?>
							</div>
						</td>
						<?php endif; if($order_detail['goods_class'] == 4): ?>
						<td style="text-align:center;"><?php echo htmlentities($order_detail['virtual_goods']['verify_total_count']); ?></td>
						<td style="text-align:center;"><?php echo htmlentities($order_detail['virtual_goods']['verify_total_count'] - $order_detail['virtual_goods']['verify_use_num']); ?></td>
						<td style="text-align:center;"><?php echo htmlentities($order_detail['virtual_code']); ?></td>
						<td>
							<?php if($order_detail['virtual_goods']['expire_time'] > 0): ?><?php echo time_to_date($order_detail['virtual_goods']['expire_time']); else: ?>永久有效<?php endif; ?></td>
						<td><a href="javascript:;" class="text-color" onclick="showVerifyRecord()">核销记录</a></td>
						<?php endif; ?>
					<?php endif; ?>
				</tr>
				<?php if(isset($order_goods_item['form'])): ?>
				<tr>
					<?php if(isset($order_detail['virtual_goods'])): if($order_detail['goods_class'] == 2): ?>
						<td colspan="10">
						<?php endif; if($order_detail['goods_class'] == 3): ?>
						<td colspan="11">
						<?php endif; if($order_detail['goods_class'] == 4): ?>
						<td colspan="10">
						<?php endif; else: ?>
					<td colspan="5">
					<?php endif; ?>
						<div class="order-goods-form">
							<?php if(is_array($order_goods_item['form']) || $order_goods_item['form'] instanceof \think\Collection || $order_goods_item['form'] instanceof \think\Paginator): if( count($order_goods_item['form'])==0 ) : echo "" ;else: foreach($order_goods_item['form'] as $key=>$vo): ?>
							<div class="form-item">
								<div class="field-title"><?php echo htmlentities($vo['value']['title']); ?>：</div>
								<div class="field-content">
									<?php if($vo['controller'] == 'Img'): if(is_array($vo['img_lists']) || $vo['img_lists'] instanceof \think\Collection || $vo['img_lists'] instanceof \think\Paginator): if( count($vo['img_lists'])==0 ) : echo "" ;else: foreach($vo['img_lists'] as $key=>$io): ?>
									<div class="form-img">
										<div class="form-img-wrap">
											<img src="<?php echo img($io); ?>" layer-src>
										</div>
									</div>
									<?php endforeach; endif; else: echo "" ;endif; else: if(isset($vo['val'])): ?>
									<?php echo htmlentities($vo['val']); ?>
									<?php endif; ?>
									<?php endif; ?>
								</div>
							</div>
							<?php endforeach; endif; else: echo "" ;endif; ?>
						</div>
					</td>
				</tr>
				<?php endif; ?>
				<?php endforeach; ?>
			</tbody>
		</table>
		<div class="table-trThree table-trFour">
			<div>
				<p>商品总额：<span>￥<?php echo htmlentities($order_detail["goods_money"]); ?></span></p>
			</div>
			<div>
				<p>店铺优惠卷：<span>￥<?php echo htmlentities($order_detail["coupon_money"]); ?></span></p>
			</div>
			<div>
				<p>店铺优惠：<span>￥<?php echo htmlentities($order_detail["promotion_money"]); ?></span></p>
			</div>
			<?php if($order_detail["point_money"] > 0): ?>
			<div>
				<p>积分抵扣：<span>￥<?php echo htmlentities($order_detail["point_money"]); ?></span></p>
			</div>
			<?php endif; if($order_detail["balance_money"] > 0): ?>
			<div>
				<p>余额：<span>￥<?php echo htmlentities($order_detail["balance_money"]); ?></span></p>
			</div>
			<?php endif; ?>
			<div>
				<p>订单调价：<span>￥<?php echo htmlentities($order_detail["adjust_money"]); ?></span></p>
			</div>
			<div>
				<p>发票费用：<span>￥<?php echo htmlentities($order_detail["invoice_money"]); ?></span></p>
			</div>
			<div>
				<p>发票邮寄费用：<span>￥<?php echo htmlentities($order_detail["invoice_delivery_money"]); ?></span></p>
			</div>
			<?php if($order_detail["member_card_money"] > 0): ?>
			<div>
				<p>会员卡：<span>￥<?php echo htmlentities($order_detail["member_card_money"]); ?></span></p>
			</div>
			<?php endif; ?>
		</div>
	    <div class="table-settlement">
			订单共<span><?php echo htmlentities($order_detail["goods_num"]); ?></span>件商品,共计<span>￥<?php echo htmlentities($order_detail["order_money"]); ?></span>
		</div>
	</div>
</div>

<?php if(!(empty($order_detail['order_log']) || (($order_detail['order_log'] instanceof \think\Collection || $order_detail['order_log'] instanceof \think\Paginator ) && $order_detail['order_log']->isEmpty()))): ?>
<!-- 订单操作 -->
<div class="shop-operation layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">订单操作日志</span>
    </div>
	<div class="shop-operation-time layui-card-body">
		<ul class="layui-timeline">
			<?php if(is_array($order_detail['order_log']) || $order_detail['order_log'] instanceof \think\Collection || $order_detail['order_log'] instanceof \think\Paginator): if( count($order_detail['order_log'])==0 ) : echo "" ;else: foreach($order_detail['order_log'] as $key=>$vo): ?>
		  	<li class="layui-timeline-item">
				<div class="layui-time-left">
					<p><?php echo date('Y-m-d', $vo['action_time']); ?></p>
					<p><?php echo date('H:i:s', $vo['action_time']); ?></p>
				</div>
			    <div class="layui-icon layui-timeline-axis">
					<span class="layui-icon-center"></span>
				</div>
			    <div class="layui-timeline-content layui-text">
			      	<div class="layui-timeline-title"><?php echo htmlentities($vo['action']); ?></div>
			    </div>
		 	</li>
		 	<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
</div>
<?php endif; ?>

<script type="text/html" id="verifyRecord">
    <div class="verify-record" style="height: 100%;overflow: auto;">
        <table class="layui-table" lay-skin="nob">
            <thead>
                <tr>
                    <th>核销人</th>
                    <th style="text-align:center">核销次数</th>
                    <th style="text-align:center">核销时间</th>
                </tr>
            </thead>
            <tbody>
                <?php if(isset($order_detail['virtual_goods']) && isset($order_detail['virtual_goods']['verify_record'])): if(is_array($order_detail['virtual_goods']['verify_record']) && count($order_detail['virtual_goods']['verify_record']) > 0): if(is_array($order_detail['virtual_goods']['verify_record']) || $order_detail['virtual_goods']['verify_record'] instanceof \think\Collection || $order_detail['virtual_goods']['verify_record'] instanceof \think\Paginator): if( count($order_detail['virtual_goods']['verify_record'])==0 ) : echo "" ;else: foreach($order_detail['virtual_goods']['verify_record'] as $key=>$vo): ?>
                        <tr>
                            <td><?php echo htmlentities($vo['verifier_name']); ?></td>
                            <td style="text-align:center">1</td>
                            <td style="text-align:center"><?php echo time_to_date($vo['verify_time']); ?></td>
                        </tr>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    <?php endif; else: ?>
                <tr>
                    <td colspan="3" style="text-align:center;">暂无核销记录</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</script>

<script type="text/javascript">
var laytpl;
var form;
var order_list = [];
var express_company_list = [];
var deliyer_list = [];
var printer_addon_is_exit = '<?php echo addon_is_exit("printer"); ?>';
var isTradeManaged = false; // 微信小程序是否已开通发货信息管理服务

function reloadList(){
    <?php if(!empty($order_detail)): ?>
		listenerHash(); // 刷新页面
		layer.closeAll();
	<?php else: ?>
		getOrderList();
	<?php endif; ?>
}
$(function () {
	// 获取物流公司
	$.ajax({
		type: "post",
		url: ns.url("shop/express/getShopExpressCompanyList"),
		dataType: 'json',
		success: function (res) {
			if (res.code == 0) {
				express_company_list = res.data;
			}
		}
	});

	// 获取配送员
	$.ajax({
		type: "post",
		url: ns.url("shop/local/getDeliverList"),
		dataType: 'json',
		success: function (res) {
			if (res.code == 0) {
				deliyer_list = res.data;
			}
		}
	});

	getOrderShippingIsTradeManaged();

});

//渲染模板引擎
layui.use(['laytpl','form'], function(){
    laytpl = layui.laytpl;
    form = layui.form;
	form.render();
    <?php if(!empty($order_detail)): ?> setOrderInfo([<?php echo json_encode($order_detail); ?>]);<?php endif; ?>
});

/**
** 设置订单信息
**/
function setOrderInfo(temp_order_list){
    var temp = {};
    temp_order_list.forEach(item => temp[item.order_id] = item);
    order_list = temp;
}

/**
 ** 获取订单信息
 **/
function getOrderInfo(order_id){
    return order_list[order_id];
}

/**
 * 订单操作
 * @param fun
 * @param order_id
 */
function orderAction(fun, order_id){
    eval(fun+"("+order_id+")");
}

// 打印发货单
function printDeliverOrder(order_id) {
	var url = ns.url("shop/printer/batchprintorder", {request_mode: 'download',order_id: order_id});
	var LODOP = getLodop();
	if (LODOP) {
		LODOP.PRINT_INIT("发货单打印");
		LODOP.ADD_PRINT_TBURL(5, 10, "770", "95%", url);
		LODOP.SET_PRINT_STYLEA(0, "HOrient", 3);
		LODOP.SET_PRINT_STYLEA(0, "VOrient", 3);
		LODOP.ADD_PRINT_TEXT(590, 680, 130, 22, "页号：第#页/共&页");
		LODOP.SET_PRINT_STYLEA(0, "ItemType", 2);
		LODOP.SET_PRINT_STYLEA(0, "Horient", 1);
		LODOP.SET_PRINT_STYLEA(0, "Vorient", 1);
		LODOP.SET_SHOW_MODE("MESSAGE_GETING_URL", ""); //该语句隐藏进度条或修改提示信息
		LODOP.PREVIEW(); //预览
	}
}

// 订单备注
function orderRemark(order_id){
    var order_info = getOrderInfo(order_id);
    layer.prompt({
        formType: 2,
        value: order_info.remark,
        title: '卖家备注',
        area: ['400px', '100px'], //自定义文本域宽高
        yes: function(index, layero){
            var value = layero.find(".layui-layer-input").val();
            if(value.trim().length > 200){
                layer.msg("备注太长，最多200个字符！");
                return false;
            }
            $.ajax({
                type: "post",
                url: ns.url("shop/order/orderRemark"),
                async: true,
                dataType: 'json',
                data: {order_id : order_id, remark : value},
                success: function (res) {
                    layer.msg(res.message);
                    if(res.code == 0){
                        layer.close(layer.index - 1);
                        reloadList();
                    }
                }
            })
        }
    });

}

// 关闭订单
var closeRepeat = false;
function orderClose(order_id){
    var temp_index = layer.confirm('确定要关闭该订单吗?', function(index) {
        if (closeRepeat) return;
        closeRepeat = true;
		layer.close(index);
        $.ajax({
            url: ns.url("shop/order/close"),
            data: {order_id : order_id},
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                layer.msg(res.message);
                if(res.code == 0){
                    layer.close(layer.index - 1);
                    reloadList();
                }
				closeRepeat = false;
            }
        });
    }, function () {
        layer.close();
        closeRepeat = false;
    });
}

/**
* 线下支付
* @param order_id
*/
var payRepeat = false;
function offlinePay(order_id){
    var order_info = getOrderInfo(order_id);
    ns.openOperateIframe({
        url:ns.url("offlinepay://shop/pay/pay", {out_trade_no: order_info.out_trade_no,member_id:order_info.member_id}),
        title:'线下支付',
        area:['700px', '500px'],
        getResFunc:'paySubmit',
        success:function (res){
            layer.msg(res.message);
            if(res.code == 0){
                reloadList();
            }
            payRepeat = false;
        }
    })
}

//线下支付审核
function offlinePayAudit(order_id){
    var order_info = getOrderInfo(order_id);
    window.open(ns.href('offlinepay://shop/pay/lists', {out_trade_no:order_info.out_trade_no}));
}

/**
 * 删除订单
 * @param order_id
 */
function orderDelete(order_id){
    layer.confirm('确定要删除该订单吗?', function(index) {
		layer.close(index);
    	$.ajax({
            url: ns.url("shop/order/delete"),
            data: {order_id : order_id},
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                layer.msg(res.message);
                if(res.code == 0){
                    reloadList();
                }
            }
        });
    }, function () {
        layer.close();
    });
}

/**
 * 确认收货
 * @param order_id
 * @param type
 */
function takeDelivery(order_id, type = 0){
    var html = "";
    if(type == 0){
        html = '确保买家已经收到您的商品，并且与买家协商完毕提前确认收货?';
    }else{
        html = '确保买家已经收到您的商品，并且与买家协商完毕提前确认收货?（退款中的订单及虚拟订单无法确认收货）';
    }
    layer.confirm(html, function(index) {
		layer.close(index);
    	$.ajax({
            url: ns.url("shop/order/takeDelivery"),
            data: {order_id : order_id, type : type},
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                layer.msg(res.message);
                if(res.code == 0){
                    reloadList();
                }
            }
        });
    }, function () {
        layer.close();
    });
}

// 打印订单小票
function printTicket(order_id){
	$.ajax({
		type: 'post',
		dataType: 'json',
		url: ns.url("shop/order/printTicket"),
		data: {order_id},
		success: function (res) {
			if (res.code != 0) {
				layer.msg(res.message ? res.message : '小票打印失败');
			}
		}
	});
}

// 查询小程序是否已开通发货信息管理服务
function getOrderShippingIsTradeManaged() {
	$.ajax({
		type: "post",
		url: ns.url("shop/order/orderShippingIsTradeManaged"),
		dataType: 'json',
		success: function (res) {
			if (res.code == 0) {
				isTradeManaged = res.data;
			}
		}
	});
}

</script>
<!-- 修改订单价格 -->
<!-- 调整价格模态 -->
<script type="text/html" id="adjust_price_html">
    <div style="padding:10px;">
        <div class="layui-form adjust-price-html" id='adjust_price'lay-filter="adjust_price">
            <div style="color: #666;">注意 : 只有订单未付款时才支持改价,改价后请联系买家刷新订单核实订单金额后再支付。</div>
            <table class="layui-table">
                <colgroup>
                    <col width="10%">
                    <col width="4%">
                    <col width="6%">
                    <col width="4%">
                    <col width="9%">
                    <col width="4%">
                    <col width="8%">
                    <col width="8%">
                    <col width="9%">
                    <col width="10%">
                    <col width="10%">
                    <col width="10%">
                    <col width="6%">
                </colgroup>
                <thead>
                    <tr>
                        <th>商品信息</th>
                        <th>单价</th>
                        <th>数量</th>
                        <th>小计</th>
                        <th>商品总额</th>
                        <th>优惠</th>
                        <th>优惠券</th>
                        <th>积分抵现</th>
                        <th>发票费用</th>
                        <th>发票邮寄费用</th>
                        <th>调整金额</th>
                        <th>运费</th>
                        <th>总计</th>
                    </tr>
                </thead>
                <tbody>
                    {{# layui.each(d.order_goods, function(index, item){ }}
                    <tr data-order_money="{{ d.order_money }}"data-adjust_money="{{ d.adjust_money }}"data-delivery_money="{{ d.delivery_money }}"
                        data-promotion_money="{{ d.promotion_money }}" data-coupon_money="{{ d.coupon_money }}" data-goods_money="{{ d.goods_money }}"
                        data-adjust_money="{{ d.adjust_money }}"data-delivery_money="{{ d.delivery_money }}" data-invoice_rate="{{ d.invoice_rate }}"
                        data-invoice_delivery_money="{{ d.invoice_delivery_money }}"  data-is_invoice="{{ d.is_invoice }}" data-point_money="{{ d.point_money }}" >
                        <td>{{ item.sku_name }}</td>
                        <td>{{ item.price }}</td>
                        <td>{{ item.num }}</td>
                        <td>{{ item.goods_money }}</td>
                        {{#  if(index == 0){ }}
                        <td rowspan="{{ d.order_goods.length }}">{{ d.goods_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}">{{ d.promotion_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}">{{ d.coupon_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}">{{ d.point_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}" class="adjust-invoice-money">{{ d.invoice_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}" class="adjust-invoice-delivery-money">{{ d.invoice_delivery_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}"><input type="number" name="adjust_money" min="{{ d.goods_money - d.promotion_money - d.coupon_money }}" class="layui-input adjust-money" onchange="adjustChange(this);" value="{{ d.adjust_money }}"/></td>
                        <td rowspan="{{ d.order_goods.length }}"><input type="number"  name="delivery_money" class="layui-input delivery-money" onchange="adjustChange(this);" value="{{ d.delivery_money }}"/></td>
                        <td rowspan="{{ d.order_goods.length }}" class="adjust-pay-money">{{ d.order_money }}</td>
                        {{#  } }}
                    </tr>
                    {{#  }); }}
                </tbody>
            </table>
            <div style="color: #666;">
                <p><a class="text-color">实际商品金额</a> = 商品总额 - 优惠金额 - 优惠券金额 - 积分抵现 + 调价</p>
                <p><a class="text-color">发票费用</a> = 实际商品金额 * 发票比率</p>
                <p>订单总额 = <a class="text-color">实际商品金额</a> + <a class="text-color">发票费用</a> + 运费 +  发票邮寄费用</p>
            </div>

            <input type="hidden" name="order_id" value="{{ d.order_id }}"/>
            <button class="layui-btn"  lay-submit id="submit_price" lay-filter="submit_price" style="display:none;">保存</button>
        </div>
    </div>
</script>

<script>
    var form;
    // 订单调价
    function orderAdjustMoney(order_id) {
        var order_info = getOrderInfo(order_id);
        var getTpl = $("#adjust_price_html").html();
        laytpl(getTpl).render(order_info, function (html) {
            layer.open({
                type: 1,
                shadeClose: true,
                shade: 0.3,
                offset: 'auto',
                scrollbar: true,
                fixed: false,
                title: "调整价格",
                area: ['1250px', 'auto'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    $("#submit_price").click();
                },
                btn2: function (index, layero) {
                    layer.close(index);
                },
                content: html,
                success: function (layero, index) {
                    var repeat_flag = false;//防重复标识
                    form.render();
                    form.on('submit(submit_price)', function (data) {
                        if (repeat_flag) return;
                        repeat_flag = true;

                        $.ajax({
                            url: ns.url("shop/order/adjustPrice"),
                            type: "POST",
                            dataType: "JSON",
                            async: false,
                            data: data.field,
                            success: function (res) {
                                layer.msg(res.message);
                                if (res.code == 0) {
                                    layer.close(layer.index - 1);
                                    reloadList();
                                } else {
                                    repeat_flag = false;
                                }

                            }
                        });
                        return false;
                    });
                }
            });
            form.render();
        });
    }

    function adjustChange(obj){
		var adjust_money = 0;
		var delivery_money = 0;
        var parent_obj = $(obj).parent().parent();
        var o_order_money = parent_obj.attr("data-order_money");
        var o_adjust_money = parent_obj.attr("data-adjust_money");
        var o_delivery_money = parent_obj.attr("data-delivery_money");
        var invoice_delivery_money = parent_obj.attr("data-invoice_delivery_money");
        var promotion_money = parent_obj.attr("data-promotion_money");
        var coupon_money = parent_obj.attr("data-coupon_money");
        var goods_money = parent_obj.attr("data-goods_money");
        var is_invoice = parent_obj.attr("data-is_invoice");
        var point_money = parent_obj.attr("data-point_money");
		$(".adjust-money").each(function(){
		adjust_money += parseFloat($(this).val());
		});
		$(".delivery-money").each(function(){
		delivery_money += parseFloat($(this).val());
		});
        var real_goods_money = parseFloat(goods_money) - parseFloat(promotion_money) - parseFloat(coupon_money) + parseFloat(adjust_money) - parseFloat(point_money);
        var invoice_rate = is_invoice == 1 ? parent_obj.attr("data-invoice_rate") : 0;
        var invoice_money = Math.round(parseFloat(real_goods_money) * parseFloat(invoice_rate)/100 * 100) / 100;
        var total_money = parseFloat(goods_money) - parseFloat(promotion_money) - parseFloat(coupon_money) - parseFloat(point_money) + parseFloat(adjust_money) + parseFloat(invoice_delivery_money) + parseFloat(invoice_money) + parseFloat(delivery_money)
        total_money = Math.round(total_money * 100) / 100;
        $(obj).parent().parent().find(".adjust-invoice-money").text(invoice_money);
        // $(obj).parent().parent().find(".adjust-invoice-delivery-money").text(total_money);
        // var total_money = parseFloat(o_order_money) - parseFloat(o_adjust_money) - parseFloat(o_delivery_money) + parseFloat(adjust_money) + parseFloat(delivery_money);
        $(".adjust-pay-money").html(total_money);
    }
</script>
<!-- 虚拟订单发货 -->
<!-- 虚拟订单物流发货 -->
<style>
    .layui-form .order_goods_list thead th, .layui-form #order_goods_list tbody tr {
        border-bottom: 1px solid #E6E6E6;
    }

    .layui-form .order_goods_list thead th {
        background-color: #F5F5F5;
        line-height: 30px;
        padding: 8px 15px;
    }
    .layui-form .order_goods_list tbody td {
        line-height: 30px;
        padding: 8px 15px;
    }

    #order_goods_list_box {
        overflow: hidden;
    }

    #order_goods_list_box table {
        margin: 0;
    }

    #order_goods_list_box table:nth-of-type(2) {
        display: block;
        overflow: auto;
        max-height: 300px;
    }
    .edit-delivery-box table{
        margin: 0;
    }
    .edit-delivery-box table:last-of-type{
        display: block;
        height: 430px;
        overflow: auto;
    }
</style>
<!--发货订单弹出框-->
<script type="text/html" id="virtual_order_delivery_html">
    <div class="order-delivery">
        <div class="layui-form">
            <input type="hidden" name="order_id" value="{{ d.order_info.order_id }}">
            <div id="order_goods_list_box">
                <table class="layui-table order_goods_list" lay-filter="order_goods" lay-skin="line" lay-filter="order_goods_list">
                    <colgroup>
                        <col width="38%">
                        <col width="15%">
                        <col width="15%">
                    </colgroup>
                    <thead>
                        <tr>
                            <th>商品名称</th>
                            <th>数量</th>
                            <th>发货状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{# layui.each(d.order_goods_list, function(index, item){ }}
                        <tr>
                            <td>{{ item.sku_name }}</td>
                            <td>{{ item.num }}</td>
                            <td>{{ item.delivery_status_name }}</td>
                        </tr>
                        {{# }); }}
                        {{# if(d.order_goods_list.length === 0){ }}
                        <tr>
                            <td colspan="3" align="center">无数据</td>
                        </tr>
                        {{# } }}
                    </tbody>
                </table>
            </div>
            <div class="form-row">
                <button type="button" class="layui-btn" lay-submit id="button_delivery_order" lay-filter="button_delivery_order" style="display:none;">保存</button>
            </div>
        </div>
    </div>
</script>

<script>
    var submitting = false;

    // 订单发货
    function orderVirtualDelivery(order_id) {
        layui.use(['table', 'form', 'laytpl'], function () {
            var laytpl = layui.laytpl, table = layui.table, form = layui.form;
            form.render();
            var getTpl = $("#virtual_order_delivery_html").html();
            var order_info = getOrderInfo(order_id);
            var data = {order_info};
            data.order_goods_list = order_info.order_goods;

            laytpl(getTpl).render(data, function (html) {
                layer.open({
                    type: 1,
                    shadeClose: true,
                    shade: 0.3,
                    fixed: false,
                    scrollbar: false,
                    title: "订单发货",
                    area: '800px',
                    btn: ['发货'],
                    yes: function (index, layero) {
                        $("#button_delivery_order").click();
                    },
                    content: html,
                    cancel: function (index, layero) {
                        //右上角关闭回调
                        layer.close(index);
                        //return false 开启该代码可禁止点击该按钮关闭
                    },
                    success: function (layero, index) {
                        form.render();

                        form.on('submit(button_delivery_order)', function (data) {
                            if (submitting) return false;
                            submitting = true;
                            $.ajax({
                                type: "post",
                                url: '<?php echo addon_url("shop/virtualorder/delivery"); ?>',
                                async: true,
                                dataType: 'json',
                                data: data.field,
                                success: function (res) {
                                    layer.msg(res.message);
                                    if (res.code == 0) {
                                        layer.close(layer.index - 1);
                                        reloadList();
                                    }
                                    submitting = false;
                                }
                            })
                        });
                    }
                });
            })
        })

    }
</script>
<!-- 主动退款 -->
<style>
    .refund-view-list{font-size:14px;line-height:20px;color:#323233;color:var(--theme-stroke-1,#323233)}
    .refund-view-item {margin-bottom: 10px;}
    .refund-view-item-label{width:75px; vertical-align: top;}
    .refund-view-item-content{display:inline-block}
    .refund-view-list .word-aux{margin-left:74px;}
</style>
<!-- 店铺主动退款 -->
<script type="text/html" id="refund_transfer_html">
    <div style="padding:10px;">
        <div class="layui-form refund-transfer-html" id='refund_transfer'lay-filter="refund_transfer">
            <div class="refund-view-list">
                <div class="refund-view-item">
                    <label class="refund-view-item-label">退款金额：</label>
                    <div class="refund-view-item-content">
                        <span class="refund-money">￥{{ d.order_goods_info.refund_apply_money }}</span>
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">主动退款：</label>
                    <div class="refund-view-item-content">
                        <input type='number' class="layui-input" name="shop_active_refund_money" value="{{d.order_goods_info.refund_apply_money}}" placeholder="0.00">
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">完成状态：</label>
                    <div class="refund-view-item-content">
                        <input type="radio" title="部分退款状态" checked name="refund_status" value="PARTIAL_REFUND">
                        <input type="radio" title="退款完成状态"  name="refund_status" value="REFUND_COMPLETE">
                    </div>
                    <div class="word-aux">
                        <div>1、如果是退部分金额，退款后可以是部分退款状态或退款完成状态</div>
                        <div>2、如果是退全部金额，则退款后一定是退款完成状态</div>
                        <div>3、退款完成才会执行相关业务如核销码失效，卡包失效等操作</div>
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">退款方式：</label>
                    <div class="refund-view-item-content">
                        <input type="radio" title="原路退款" checked name="shop_active_refund_money_type" value="1">
                        <input type="radio" title="线下退款"  name="shop_active_refund_money_type" value="2">
                        <input type="radio" title="退款到余额"  name="shop_active_refund_money_type" value="3">
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">退款说明：</label>
                    <div class="refund-view-item-content">
                        <textarea name="shop_active_refund_remark" class="layui-textarea len-long" maxlength="150"></textarea>
                    </div>
                </div>

            </div>
            <input type="hidden" name="order_goods_id" value="{{ d.order_goods_info.order_goods_id }}"/>
            <button class="layui-btn"  lay-submit id="submit_transfer" lay-filter="submit_transfer" style="display:none;">保存</button>
        </div>
    </div>
</script>

<script>
    var laytpl,form,active_refund_layer;
    layui.use(['laytpl','form'], function(){
        laytpl = layui.laytpl;
        form = layui.form;
        form.render();
    });

    // 主动退款
    function shopActiveRefund(order_goods_id) {
        $.ajax({
            url: ns.url("shop/orderrefund/getOrderGoodsRefundInfo"),
            type: "POST",
            dataType: "JSON",
            async: false,
            data: {order_goods_id: order_goods_id},
            success: function (res) {
                if (res.code >= 0) {
                    var getTpl = $("#refund_transfer_html").html();
                    var refund_data = res.data;
                    laytpl(getTpl).render(refund_data, function (html) {
                        active_refund_layer = layer.open({
                            type: 1,
                            shadeClose: true,
                            shade: 0.3,
                            offset: 'auto',
                            scrollbar: true,
                            fixed: false,
                            title: "店铺主动退款",
                            area: ['700px', 'auto'],
                            btn: ['确认退款', '取消'],
                            yes: function (index, layero) {
                                $("#submit_transfer").click();
                            },
                            btn2: function (index, layero) {
                                layer.close(index);
                            },
                            content: html,
                            success: function (layero, index) {
                                var repeat_flag = false;//防重复标识
                                form.render();

                                form.on('submit(submit_transfer)', function (data) {
                                    if(!ns.getRegexp('>=0float2').test(data.field.shop_active_refund_money)){
                                        layer.msg('请输入正确的退款金额，最多保留两位小数');
                                        return;
                                    }
                                    if(Number(data.field.shop_active_refund_money) > Number(refund_data.order_goods_info.refund_apply_money)){
                                        layer.msg('主动退款金额不能大于可退款总额');
                                        return;
                                    }
                                    if (repeat_flag) return;
                                    repeat_flag = true;
                                    $.ajax({
                                        url: ns.url("shop/orderrefund/shopActiveRefund"),
                                        type: "POST",
                                        dataType: "JSON",
                                        data: data.field,
                                        beforeSend: function () {layer_index = layer.load();},
                                        complete: function () {layer.close(layer_index);},
                                        success: function (res) {
                                            layer.msg(res.message);
                                            if (res.code == 0) {
                                                layer.close(active_refund_layer);
                                                reloadList();
                                            } else {
                                                repeat_flag = false;
                                            }

                                        }
                                    });
                                    return false;
                                });
                            }
                        });
                        form.render();
                    });
                } else {
                    layer.msg(res.message);
                }
            }
        });

    }
</script>
<script src="http://**********/app/shop/view/public/js/lodop_funcs.js"></script>
<script>
	function showVerifyRecord(){
	    layer.open({
	        title: '核销记录',
	        skin: 'verify-record',
	        type: 1,
	        area: ['710px', '60%'],
	        content: $('#verifyRecord').html()
	    })
	}
	function toMemberDetail(){
		let member_id = '<?php echo htmlentities($order_detail['member_id']); ?>';
		window.open(ns.href("shop/member/editmember", {member_id:member_id}));
	}
</script>