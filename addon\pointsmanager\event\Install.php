<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\pointsmanager\event;

use think\facade\Db;

/**
 * 插件安装事件
 */
class Install
{
    /**
     * 执行安装
     */
    public function handle()
    {
        try {
            // 创建积分记录表
            $this->createPointsRecordTable();
            
            // 创建积分规则表
            $this->createPointsRuleTable();
            
            // 初始化默认积分规则
            $this->initDefaultRules();
            
            return success('积分管理插件安装成功');
        } catch (\Exception $e) {
            return error('', '安装失败：' . $e->getMessage());
        }
    }
    
    /**
     * 创建积分记录表
     */
    private function createPointsRecordTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `ns_points_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
            `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点ID',
            `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
            `points` int(11) NOT NULL DEFAULT '0' COMMENT '积分数量',
            `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '类型：1增加 2减少',
            `source` varchar(50) NOT NULL DEFAULT '' COMMENT '积分来源',
            `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
            `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
            PRIMARY KEY (`id`),
            KEY `member_id` (`member_id`),
            KEY `site_id` (`site_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';";
        
        Db::execute($sql);
    }
    
    /**
     * 创建积分规则表
     */
    private function createPointsRuleTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `ns_points_rule` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
            `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点ID',
            `name` varchar(100) NOT NULL DEFAULT '' COMMENT '规则名称',
            `code` varchar(50) NOT NULL DEFAULT '' COMMENT '规则代码',
            `points` int(11) NOT NULL DEFAULT '0' COMMENT '积分数量',
            `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1启用 0禁用',
            `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
            `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
            `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `site_id` (`site_id`),
            KEY `code` (`code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分规则表';";
        
        Db::execute($sql);
    }
    
    /**
     * 初始化默认积分规则
     */
    private function initDefaultRules()
    {
        $rules = [
            [
                'site_id' => 1,
                'name' => '会员注册',
                'code' => 'register',
                'points' => 100,
                'status' => 1,
                'remark' => '新会员注册赠送积分',
                'create_time' => time(),
                'update_time' => time(),
            ],
            [
                'site_id' => 1,
                'name' => '每日签到',
                'code' => 'signin',
                'points' => 10,
                'status' => 1,
                'remark' => '每日签到赠送积分',
                'create_time' => time(),
                'update_time' => time(),
            ],
            [
                'site_id' => 1,
                'name' => '订单消费',
                'code' => 'order_pay',
                'points' => 1,
                'status' => 1,
                'remark' => '每消费1元赠送1积分',
                'create_time' => time(),
                'update_time' => time(),
            ],
        ];
        
        Db::name('points_rule')->insertAll($rules);
    }
}
