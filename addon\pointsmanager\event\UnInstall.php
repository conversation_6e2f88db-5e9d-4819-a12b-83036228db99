<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\pointsmanager\event;

use think\facade\Db;

/**
 * 插件卸载事件
 */
class UnInstall
{
    /**
     * 执行卸载
     */
    public function handle()
    {
        try {
            // 删除积分记录表
            Db::execute("DROP TABLE IF EXISTS `ns_points_record`");
            
            // 删除积分规则表
            Db::execute("DROP TABLE IF EXISTS `ns_points_rule`");
            
            return success('积分管理插件卸载成功');
        } catch (\Exception $e) {
            return error('', '卸载失败：' . $e->getMessage());
        }
    }
}
