# 会员积分管理插件

## 插件介绍

这是一个演示插件，用于管理会员积分记录、规则设置和统计分析。

## 功能特性

1. **积分记录管理**
   - 查看所有积分变动记录
   - 支持按会员、类型、来源筛选
   - 详细的积分变动信息

2. **积分规则设置**
   - 灵活的积分规则配置
   - 支持多种积分获取场景
   - 规则启用/禁用控制

3. **积分统计分析**
   - 总积分发放统计
   - 今日/本月积分统计
   - 积分消耗统计

4. **事件驱动**
   - 会员注册自动赠送积分
   - 订单支付自动赠送积分
   - 会员签到自动赠送积分

## 安装说明

1. 将插件文件放置到 `addon/pointsmanager/` 目录
2. 在后台插件管理中安装插件
3. 安装完成后会自动创建相关数据表
4. 配置积分规则开始使用

## 文件结构

```
addon/pointsmanager/
├── config/                 # 配置文件
│   ├── info.php            # 插件信息
│   ├── event.php           # 事件配置
│   └── menu_shop.php       # 菜单配置
├── event/                  # 事件处理
│   ├── Install.php         # 安装事件
│   ├── UnInstall.php       # 卸载事件
│   └── OrderPay.php        # 订单支付事件
├── model/                  # 数据模型
│   ├── PointsRecord.php    # 积分记录模型
│   └── PointsRule.php      # 积分规则模型
├── shop/                   # 商家端
│   ├── controller/         # 控制器
│   └── view/              # 视图模板
└── README.md              # 说明文档
```

## 数据表说明

### ns_points_record (积分记录表)
- id: 记录ID
- site_id: 站点ID
- member_id: 会员ID
- points: 积分数量
- type: 类型(1增加 2减少)
- source: 积分来源
- remark: 备注
- create_time: 创建时间

### ns_points_rule (积分规则表)
- id: 规则ID
- site_id: 站点ID
- name: 规则名称
- code: 规则代码
- points: 积分数量
- status: 状态(1启用 0禁用)
- remark: 备注
- create_time: 创建时间
- update_time: 更新时间

## 注意事项

1. 需要在 `addon/pointsmanager/` 目录下放置一个 `icon.png` 文件作为插件图标
2. 图标尺寸建议为 64x64 像素
3. 插件卸载时会删除所有相关数据，请谨慎操作

## 版本信息

- 版本号：1.0.0
- 开发者：演示开发者
- 适用系统：Niushop V5.4.9+
