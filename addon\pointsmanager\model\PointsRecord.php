<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\pointsmanager\model;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 积分记录模型
 */
class PointsRecord extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'points_record';

    /**
     * 添加积分记录
     * @param array $data
     * @return array
     */
    public function addRecord($data)
    {
        try {
            $data['create_time'] = time();
            $result = $this->save($data);
            
            if ($result) {
                return $this->success($result);
            } else {
                return $this->error();
            }
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 获取积分记录列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param int $limit
     * @return array
     */
    public function getRecordList($condition = [], $field = '*', $order = 'create_time desc', $limit = 0)
    {
        try {
            $list = $this->field($field)
                ->where($condition)
                ->order($order);
            
            if ($limit > 0) {
                $list = $list->limit($limit);
            }
            
            $list = $list->select()->toArray();
            
            // 关联会员信息
            if (!empty($list)) {
                $member_ids = array_column($list, 'member_id');
                $members = Db::name('member')
                    ->whereIn('member_id', $member_ids)
                    ->column('nickname,mobile', 'member_id');
                
                foreach ($list as &$item) {
                    $item['member_info'] = $members[$item['member_id']] ?? [];
                    $item['type_text'] = $item['type'] == 1 ? '增加' : '减少';
                    $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                }
            }
            
            return $this->success($list);
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 获取积分统计
     * @param array $condition
     * @return array
     */
    public function getStatistics($condition = [])
    {
        try {
            // 总积分发放
            $total_add = $this->where($condition)
                ->where('type', 1)
                ->sum('points');
            
            // 总积分消耗
            $total_reduce = $this->where($condition)
                ->where('type', 2)
                ->sum('points');
            
            // 净积分
            $net_points = $total_add - $total_reduce;
            
            // 今日积分发放
            $today_start = strtotime(date('Y-m-d'));
            $today_add = $this->where($condition)
                ->where('type', 1)
                ->where('create_time', '>=', $today_start)
                ->sum('points');
            
            // 本月积分发放
            $month_start = strtotime(date('Y-m-01'));
            $month_add = $this->where($condition)
                ->where('type', 1)
                ->where('create_time', '>=', $month_start)
                ->sum('points');
            
            $statistics = [
                'total_add' => $total_add ?: 0,
                'total_reduce' => $total_reduce ?: 0,
                'net_points' => $net_points,
                'today_add' => $today_add ?: 0,
                'month_add' => $month_add ?: 0,
            ];
            
            return $this->success($statistics);
        } catch (\Exception $e) {
            return $this->error('', $e->getMessage());
        }
    }

    /**
     * 为会员添加积分
     * @param int $member_id
     * @param int $points
     * @param string $source
     * @param string $remark
     * @return array
     */
    public function addPointsToMember($member_id, $points, $source = '', $remark = '')
    {
        try {
            Db::startTrans();
            
            // 添加积分记录
            $record_data = [
                'site_id' => 1,
                'member_id' => $member_id,
                'points' => $points,
                'type' => $points > 0 ? 1 : 2,
                'source' => $source,
                'remark' => $remark,
                'create_time' => time(),
            ];
            
            $this->save($record_data);
            
            // 更新会员积分
            Db::name('member')->where('member_id', $member_id)->inc('point', $points);
            
            Db::commit();
            return $this->success();
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', $e->getMessage());
        }
    }
}
