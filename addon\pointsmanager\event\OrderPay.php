<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\pointsmanager\event;

use addon\pointsmanager\model\PointsRecord;
use addon\pointsmanager\model\PointsRule;

/**
 * 订单支付事件 - 赠送消费积分
 */
class OrderPay
{
    /**
     * 处理订单支付事件
     * @param array $params
     */
    public function handle($params)
    {
        try {
            // 获取订单信息
            $order_id = $params['order_id'] ?? 0;
            $member_id = $params['member_id'] ?? 0;
            $order_money = $params['order_money'] ?? 0;
            
            if ($order_id <= 0 || $member_id <= 0 || $order_money <= 0) {
                return;
            }
            
            // 获取消费积分规则
            $points_rule = new PointsRule();
            $rule_result = $points_rule->getRuleByCode('order_pay');
            
            if ($rule_result['code'] != 0) {
                return; // 规则不存在或已禁用
            }
            
            $rule = $rule_result['data'];
            
            // 计算积分：每消费1元赠送对应积分
            $points = floor($order_money * $rule['points']);
            
            if ($points > 0) {
                // 添加积分记录
                $points_record = new PointsRecord();
                $points_record->addPointsToMember(
                    $member_id,
                    $points,
                    '订单消费',
                    '订单号：' . $order_id . '，消费金额：' . $order_money . '元'
                );
            }
            
        } catch (\Exception $e) {
            // 记录错误日志
            trace('积分管理插件-订单支付事件处理失败：' . $e->getMessage(), 'error');
        }
    }
}
