<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>积分记录</title>
    <link rel="stylesheet" href="__STATIC__/ext/layui/css/layui.css">
</head>
<body>
    <div class="layui-container" style="margin-top: 20px;">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>积分记录</h3>
                    </div>
                    <div class="layui-card-body">
                        <!-- 搜索表单 -->
                        <form class="layui-form" lay-filter="searchForm">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md3">
                                    <input type="text" name="member_id" placeholder="会员ID" class="layui-input">
                                </div>
                                <div class="layui-col-md3">
                                    <select name="type">
                                        <option value="">积分类型</option>
                                        <option value="1">增加</option>
                                        <option value="2">减少</option>
                                    </select>
                                </div>
                                <div class="layui-col-md3">
                                    <input type="text" name="source" placeholder="积分来源" class="layui-input">
                                </div>
                                <div class="layui-col-md3">
                                    <button type="button" class="layui-btn" onclick="searchRecords()">
                                        <i class="layui-icon layui-icon-search"></i> 搜索
                                    </button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                        
                        <!-- 数据表格 -->
                        <table class="layui-table" lay-filter="recordsTable" id="recordsTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            
            // 渲染表格
            table.render({
                elem: '#recordsTable',
                url: '',
                method: 'POST',
                page: true,
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'member_info', title: '会员信息', width: 150, templet: function(d){
                        if (d.member_info && d.member_info.nickname) {
                            return d.member_info.nickname + '<br><small>' + (d.member_info.mobile || '') + '</small>';
                        }
                        return '会员ID:' + d.member_id;
                    }},
                    {field: 'points', title: '积分数量', width: 100, templet: function(d){
                        var color = d.type == 1 ? 'green' : 'red';
                        var symbol = d.type == 1 ? '+' : '-';
                        return '<span style="color:' + color + '">' + symbol + d.points + '</span>';
                    }},
                    {field: 'type_text', title: '类型', width: 80},
                    {field: 'source', title: '来源', width: 120},
                    {field: 'remark', title: '备注', width: 200},
                    {field: 'create_time_text', title: '创建时间', width: 160}
                ]],
                response: {
                    statusCode: 0
                },
                parseData: function(res){
                    return {
                        "code": res.code,
                        "msg": res.message,
                        "count": res.data ? res.data.total : 0,
                        "data": res.data ? res.data.list : []
                    };
                }
            });
        });
        
        function searchRecords() {
            var formData = {};
            $('.layui-form input, .layui-form select').each(function(){
                var name = $(this).attr('name');
                var value = $(this).val();
                if (name && value) {
                    formData[name] = value;
                }
            });
            
            layui.table.reload('recordsTable', {
                where: formData,
                page: {
                    curr: 1
                }
            });
        }
    </script>
</body>
</html>
